$ EZ-EDS Version 3.29.1.20210209 Generated Electronic Data Sheet

[File]
        DescText = "EDS file for the GSIO-B-EIC-1616P";
        CreateDate = 27-3-2023;
        CreateTime = 11:00:00;
        ModDate = 27-3-2023;
        ModTime = 11:00:00;
        Revision = 3.29;


[Device]
        VendCode = 32764;
        VendName = "GIENSO";
        ProdType = 7;
        ProdTypeStr = "General Purpose Discrete I/O";
        ProdCode = 10006;
        MajRev = 2;
        MinRev = 2;
        ProdName = "GSIO-B-EIC-1616P";
        Catalog = "GSIO-B-EIC-1616P";

[Device Classification]
        Class1 = EtherNetIP;

[Params]
        Param1 =
                0,                      $ reserved, shall equal 0
                ,,                      $ Link Path Size, Link Path
                0x0000,                 $ Descriptor
                0xC7,                   $ Data Type
                2,                      $ Data Size in bytes
                "Input Data",           $ name
                "",                     $ units
                "",                     $ help string
                0,512,2,                $ min, max, default data values
                ,,,,                    $ mult, div, base, offset scaling
                ,,,,                    $ mult, div, base, offset links
                ;                       $ decimal places
        Param2 =
                0,                      $ reserved, shall equal 0
                ,,                      $ Link Path Size, Link Path
                0x0000,                 $ Descriptor
                0xC7,                   $ Data Type
                2,                      $ Data Size in bytes
                "Output Data",          $ name
                "",                     $ units
                "",                     $ help string
                0,512,2,                $ min, max, default data values
                ,,,,                    $ mult, div, base, offset scaling
                ,,,,                    $ mult, div, base, offset links
                ;                       $ decimal places
        Param3 =
                0,                      $ reserved, shall equal 0
                ,,                      $ Link Path Size, Link Path
                0x0000,                 $ Descriptor
                0xC8,                   $ Data Type
                4,                      $ Data Size in bytes
                "RPI",                  $ name
                "",                     $ units
                "",                     $ help string
                500,50000,20000,        $ min, max, default data values
                ,,,,                    $ mult, div, base, offset scaling
                ,,,,                    $ mult, div, base, offset links
                ;                       $ decimal places

[Assembly]
        Object_Name = "Assembly Object";
        Object_Class_Code = 0x04;
        Number_Of_Static_Instances = 6;
        Assem100 =
                "Input Assembly",
                "",
                2,
                0x0001,
                ,,
                16,Param1;

        Assem150 =
                "Output Assembly",
                "",
                2,
                0x0001,
                ,,
                16,Param2;


[Connection Manager]
        Revision = 1;
        Object_Name = "Connection Manager Object";
        Object_Class_Code = 0x06;
        MaxInst = 1;
        Number_Of_Static_Instances = 1;
        Max_Number_Of_Dynamic_Instances = 0;
        Connection1 =
                0x84010002,             $ 0-15    = supported transport classes
                                        $ 16      = trigger: cyclic
                                        $ 17      = trigger: change of state
                                        $ 18      = trigger: application
                                        $ 19-23   = trigger: reserved
                                        $ 24      = transport type: listen-only
                                        $ 25      = transport type: input-only
                                        $ 26      = transport type: exclusive-owner
                                        $ 27      = transport type: redundant-owner
                                        $ 28-30   = reserved
                                        $ 31      = Client = 0 / Server = 1
                0x44440405,             $ 0       = O->T fixed size supported
                                        $ 1       = O->T variable size supported
                                        $ 2       = T->O fixed size supported
                                        $ 3       = T->O variable size supported
                                        $ 4-5     = O->T number of bytes per slot (obsolete)
                                        $ 6-7     = T->O number of bytes per slot (obsolete)
                                        $ 8-10    = O->T Real time transfer format
                                        $ 11      = reserved
                                        $ 12-14   = T->O Real time transfer format
                                        $ 15      = reserved
                                        $ 16      = O->T connection type: NULL
                                        $ 17      = O->T connection type: MULTICAST
                                        $ 18      = O->T connection type: POINT2POINT
                                        $ 19      = O->T connection type: reserved
                                        $ 20      = T->O connection type: NULL
                                        $ 21      = T->O connection type: MULTICAST
                                        $ 22      = T->O connection type: POINT2POINT
                                        $ 23      = T->O connection type: reserved
                                        $ 24      = O->T priority: LOW
                                        $ 25      = O->T priority: HIGH
                                        $ 26      = O->T priority: SCHEDULED
                                        $ 27      = O->T priority: reserved
                                        $ 28      = T->O priority: LOW
                                        $ 29      = T->O priority: HIGH
                                        $ 30      = T->O priority: SCHEDULED
                                        $ 31      = T->O priority: reserved
                Param3,Param2,Assem150, $ O->T RPI, size, format
                Param3,Param1,Assem100, $ T->O RPI, size, format
                ,,                      $ config #1 size, format
                ,,		                $ config #2 size, format
                "Exlusive Owner",       $ Connection Name
                "",                     $ help string
                "20 04 24 97 2C 96 2C 64";    $ Path
								

[Capacity]
        MaxMsgConnections = 6;
        MaxIOProduceConsume = 2;
        MaxIOMcastProducers = 1;
        MaxIOMcastConsumers = 1;
        MaxConsumersPerMcast = 6;
        TSpec1 = TxRx, 32, 6000;

[TCP/IP Interface Class]
        Revision = 4;
        Object_Name = "TCP/IP Interface Object";
        Object_Class_Code = 0xF5;
        MaxInst = 1;
        Number_Of_Static_Instances = 1;
        Max_Number_Of_Dynamic_Instances = 0;

[Ethernet Link Class]
        Revision = 4;
        Object_Name = "Ethernet Link Object";
        Object_Class_Code = 0xF6;
        MaxInst = 1;
        Number_Of_Static_Instances = 1;
        Max_Number_Of_Dynamic_Instances = 0;

[Identity Class]
        Revision = 1;
        Object_Name = "Identity Object";
        Object_Class_Code = 0x01;
        MaxInst = 1;
        Number_Of_Static_Instances = 1;
        Max_Number_Of_Dynamic_Instances = 0;

[QoS Class]
        Revision = 1;
        Object_Name = "QoS Object";
        Object_Class_Code = 0x48;
        MaxInst = 1;
        Number_Of_Static_Instances = 1;
        Max_Number_Of_Dynamic_Instances = 0;

