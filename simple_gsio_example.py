#!/usr/bin/env python3
"""
GSIO-B-EIC-1616P 简单控制示例
使用pycomm3库控制16输入16输出EtherNet/IP模块
"""

import time
import struct
from pycomm3 import CIPDriver, Services, ClassCode, UINT


def simple_io_control():
    """简单的IO控制示例"""
    
    # 设备IP地址 - 请根据实际情况修改
    DEVICE_IP = "***************"
    
    # 根据EDS文件的Assembly配置
    INPUT_ASSEMBLY = 100    # 输入Assembly实例
    OUTPUT_ASSEMBLY = 150   # 输出Assembly实例
    
    try:
        # 创建连接
        with CIPDriver(DEVICE_IP) as plc:
            print(f"连接到GSIO设备: {DEVICE_IP}")
            
            # 获取设备信息
            try:
                device_info = plc.list_identity()
                print(f"设备信息: {device_info}")
            except:
                print("无法获取设备信息，继续执行...")

            # 示例1: 读取输入状态
            print("\n=== 读取输入状态 ===")
            try:
                # 使用generic_message读取Assembly数据
                input_result = plc.generic_message(
                    service=Services.get_attribute_single,
                    class_code=ClassCode.assembly,
                    instance=INPUT_ASSEMBLY,
                    attribute=3,            # Data attribute
                    data_type=UINT,
                    connected=True,
                    unconnected_send=False,
                    route_path=False,
                    name='read_inputs'
                )

                if input_result:
                    input_value = input_result.value
                    print(f"输入状态: 0x{input_value:04X} ({input_value:016b})")

                    # 显示各个输入位状态
                    for i in range(16):
                        bit_state = "ON " if (input_value & (1 << i)) else "OFF"
                        print(f"输入{i:2d}: {bit_state}")
                else:
                    print(f"读取输入失败: {input_result.error if input_result else '无响应'}")

            except Exception as e:
                print(f"读取输入失败: {e}")
            
            # 示例2: 控制输出
            print("\n=== 控制输出示例 ===")

            # 逐个点亮输出
            for i in range(16):
                try:
                    output_value = 1 << i  # 只点亮第i位

                    result = plc.generic_message(
                        service=Services.set_attribute_single,
                        class_code=ClassCode.assembly,
                        instance=OUTPUT_ASSEMBLY,
                        attribute=3,            # Data attribute
                        request_data=UINT.encode(output_value),
                        connected=True,
                        unconnected_send=False,
                        route_path=False,
                        name='write_outputs'
                    )

                    if result:
                        print(f"点亮输出{i} (0x{output_value:04X})")
                    else:
                        print(f"设置输出{i}失败: {result.error if result else '无响应'}")

                    time.sleep(0.5)  # 延时500ms

                except Exception as e:
                    print(f"控制输出{i}异常: {e}")

            # 关闭所有输出
            try:
                result = plc.generic_message(
                    service=Services.set_attribute_single,
                    class_code=ClassCode.assembly,
                    instance=OUTPUT_ASSEMBLY,
                    attribute=3,
                    request_data=UINT.encode(0),
                    connected=True,
                    unconnected_send=False,
                    route_path=False,
                    name='clear_outputs'
                )
                if result:
                    print("关闭所有输出")
                else:
                    print(f"关闭输出失败: {result.error if result else '无响应'}")
            except Exception as e:
                print(f"关闭输出失败: {e}")
                
    except Exception as e:
        print(f"连接或操作失败: {e}")


def monitor_inputs(device_ip: str, duration: int = 10):
    """监控输入状态变化"""

    INPUT_ASSEMBLY = 100

    try:
        with CIPDriver(device_ip) as plc:
            print(f"监控输入状态 {duration} 秒...")

            last_input = None
            start_time = time.time()

            while time.time() - start_time < duration:
                try:
                    result = plc.generic_message(
                        service=Services.get_attribute_single,
                        class_code=ClassCode.assembly,
                        instance=INPUT_ASSEMBLY,
                        attribute=3,
                        data_type=UINT,
                        connected=True,
                        unconnected_send=False,
                        route_path=False,
                        name='monitor_inputs'
                    )

                    if result:
                        current_input = result.value

                        if current_input != last_input:
                            print(f"输入变化: 0x{current_input:04X} ({current_input:016b})")

                            # 显示变化的位
                            if last_input is not None:
                                changed = current_input ^ last_input
                                for i in range(16):
                                    if changed & (1 << i):
                                        state = "ON " if (current_input & (1 << i)) else "OFF"
                                        print(f"  输入{i}: {state}")

                            last_input = current_input

                    time.sleep(0.1)  # 100ms采样间隔

                except Exception as e:
                    print(f"读取异常: {e}")
                    time.sleep(1)

    except Exception as e:
        print(f"监控失败: {e}")


def pattern_output(device_ip: str):
    """输出模式测试"""

    OUTPUT_ASSEMBLY = 150

    patterns = [
        0x0001,  # 单点
        0x0003,  # 两点
        0x000F,  # 四点
        0x00FF,  # 八点
        0xFFFF,  # 全亮
        0x5555,  # 间隔
        0xAAAA,  # 间隔反向
        0x0000,  # 全灭
    ]

    try:
        with CIPDriver(device_ip) as plc:
            print("输出模式测试...")

            for i, pattern in enumerate(patterns):
                try:
                    result = plc.generic_message(
                        service=Services.set_attribute_single,
                        class_code=ClassCode.assembly,
                        instance=OUTPUT_ASSEMBLY,
                        attribute=3,
                        request_data=UINT.encode(pattern),
                        connected=True,
                        unconnected_send=False,
                        route_path=False,
                        name='pattern_output'
                    )

                    if result:
                        print(f"模式{i+1}: 0x{pattern:04X} ({pattern:016b})")
                    else:
                        print(f"模式{i+1}设置失败: {result.error if result else '无响应'}")

                    time.sleep(1)

                except Exception as e:
                    print(f"模式{i+1}异常: {e}")

    except Exception as e:
        print(f"模式测试失败: {e}")


if __name__ == "__main__":
    print("=== GSIO-B-EIC-1616P EtherNet/IP 控制示例 ===")
    print("请确保:")
    print("1. 已安装pycomm3: pip install pycomm3")
    print("2. 设备IP地址正确")
    print("3. 网络连接正常")
    print()
    
    # 修改为实际的设备IP地址
    DEVICE_IP = "***************"
    
    try:
        choice = input("选择操作:\n1. 基本IO控制\n2. 监控输入(10秒)\n3. 输出模式测试\n请输入选择(1-3): ")
        
        if choice == "1":
            simple_io_control()
        elif choice == "2":
            monitor_inputs(DEVICE_IP)
        elif choice == "3":
            pattern_output(DEVICE_IP)
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n程序被中断")
    except Exception as e:
        print(f"程序异常: {e}")
