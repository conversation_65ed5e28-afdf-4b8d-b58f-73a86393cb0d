import time
from pycomm3 import CIPDriver, Pycomm3Error

# --- 1. 配置设备参数 ---
# !!! 重要: 请将此 IP 地址更改为您 GSIO-B-EIC-1616P 模块的实际 IP 地址 !!!
DEVICE_IP = '************'  # <-- 替换成你的设备 IP

# 从 EDS 文件解析出的连接参数
# T->O (Target to Originator) or Input data
INPUT_ASSEMBLY = 100
INPUT_SIZE_BYTES = 2  # 2 字节 = 16 位输入

# O->T (Originator to Target) or Output data
OUTPUT_ASSEMBLY = 150
OUTPUT_SIZE_BYTES = 2 # 2 字节 = 16 位输出

# Configuration Assembly (无配置数据)
CONFIG_ASSEMBLY = 1 # 通常为 1，且大小为 0
CONFIG_SIZE_BYTES = 0

# Requested Packet Interval (RPI) in microseconds
RPI_MS = 20  # 20ms
RPI_US = RPI_MS * 1000

def main():
    """
    主函数，用于连接和控制 EtherNet/IP I/O 模块
    """
    print(f"正在尝试连接到 EtherNet/IP 设备: {DEVICE_IP}...")
    
    # 使用 with 语句确保连接被正确打开和关闭
    try:
        with CIPDriver(DEVICE_IP) as driver:
            print("设备驱动已打开，正在建立 I/O 连接...")
            
            # 使用 generic_connect 建立与通用 EtherNet/IP 设备的 I/O 连接
            with driver.generic_connect(
                o_t_assembly=(OUTPUT_ASSEMBLY, OUTPUT_SIZE_BYTES), # 输出
                t_o_assembly=(INPUT_ASSEMBLY, INPUT_SIZE_BYTES),   # 输入
                config_assembly=(CONFIG_ASSEMBLY, CONFIG_SIZE_BYTES), # 配置
                rpi=RPI_US,
            ) as conn:
                print("I/O 连接成功建立！开始循环读写数据...")
                print("按 Ctrl+C 退出程序。")

                output_value = 0
                
                while True:
                    # --- 2. 读取输入数据 ---
                    # conn.input_data 是一个 bytes 对象
                    if conn.input_data:
                        # 将 2 字节的输入数据转换为一个整数
                        input_status = int.from_bytes(conn.input_data, 'little')
                        # 以 16 位二进制格式打印输入状态，方便观察
                        print(f"输入状态: {input_status:016b}")

                    # --- 3. 写入输出数据 ---
                    # 这是一个简单的示例：循环点亮第 0 到 15 个输出点
                    
                    # 计算要点亮的位 (0-15)
                    bit_to_light = output_value % 16
                    # 构造输出的整数值 (例如，点亮第3位就是 2^3 = 8)
                    current_output = 1 << bit_to_light
                    
                    print(f"  -> 正在写入输出: {current_output:016b} (点亮第 {bit_to_light} 位)")
                    
                    # 将整数转换为 2 字节的 little-endian 格式并写入
                    conn.output_data = current_output.to_bytes(OUTPUT_SIZE_BYTES, 'little')
                    
                    output_value += 1
                    
                    # 等待一段时间，避免刷屏太快
                    time.sleep(1) # 每秒更新一次输出

    except Pycomm3Error as e:
        print(f"发生通信错误: {e}")
    except KeyboardInterrupt:
        print("\n程序被用户中断。正在关闭连接...")
    except Exception as e:
        print(f"发生未知错误: {e}")
    finally:
        print("程序结束。")

if __name__ == '__main__':
    main()