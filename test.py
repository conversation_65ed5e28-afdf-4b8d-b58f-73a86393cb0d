import time
from pycomm3 import CIPDriver

# --- 1. 配置设备参数 ---
# !!! 请再次确认此 IP 地址是您设备的实际地址 !!!
DEVICE_IP = '***************'  # <-- 替换成你的设备 IP

# 从 EDS 文件解析出的连接参数
# T->O (Target to Originator) or Input data
INPUT_ASSEMBLY = 100
INPUT_SIZE_BYTES = 2  # 2 字节 = 16 位输入

# O->T (Originator to Target) or Output data
OUTPUT_ASSEMBLY = 150
OUTPUT_SIZE_BYTES = 2 # 2 字节 = 16 位输出

# Configuration Assembly (根据EDS文件，无配置数据)
CONFIG_ASSEMBLY = 1   # 对于无配置的设备，实例号通常为 1
CONFIG_SIZE_BYTES = 0

# Requested Packet Interval (RPI) in microseconds
RPI_US = 20000  # 20ms, 从 EDS Param3 的默认值获取

def main():
    """
    主函数，用于建立正确的 I/O 连接并控制模块
    """
    print(f"正在尝试连接到 EtherNet/IP 设备: {DEVICE_IP}...")
    
    # 使用 with 语句确保连接被正确打开和关闭
    try:
        # 步骤 1: 创建驱动实例
        with CIPDriver(DEVICE_IP) as driver:
            print("设备驱动已打开，正在建立 Class 1 (隐式/I/O) 连接...")
            
            # 步骤 2: 使用 generic_connect 建立 I/O 连接，这是控制 I/O 的关键！
            # 这个方法会处理所有底层的 ForwardOpen 请求，是专门为通用 I/O 设备设计的。
            with driver.generic_connect(
                o_t_assembly=(OUTPUT_ASSEMBLY, OUTPUT_SIZE_BYTES),
                t_o_assembly=(INPUT_ASSEMBLY, INPUT_SIZE_BYTES),
                config_assembly=(CONFIG_ASSEMBLY, CONFIG_SIZE_BYTES),
                rpi=RPI_US,
            ) as conn:
                print("\n✓✓✓ I/O 连接成功建立！现在可以实时读写数据。✓✓✓")
                print("按 Ctrl+C 退出程序。\n")

                output_value = 0
                
                while True:
                    # --- 读取输入数据 ---
                    # conn.input_data 由 pycomm3 在后台自动更新
                    if conn.input_data is not None:
                        input_status = int.from_bytes(conn.input_data, 'little')
                        print(f"实时输入: {input_status:016b}  (整数值: {input_status})")
                    else:
                        print("等待第一次输入数据...")

                    # --- 写入输出数据 ---
                    # 示例：每0.5秒切换点亮下一个输出点
                    bit_to_light = output_value % 16
                    current_output = 1 << bit_to_light
                    
                    print(f"  -> 写入输出: {current_output:016b}  (点亮第 {bit_to_light} 位)")
                    
                    # 向 conn.output_data 赋值，数据会自动发送给设备
                    conn.output_data = current_output.to_bytes(OUTPUT_SIZE_BYTES, 'little')
                    
                    output_value += 1
                    time.sleep(0.5)

    except KeyboardInterrupt:
        print("\n程序被用户中断。正在安全关闭连接...")
    except Exception as e:
        print(f"\n发生未知错误: {e}")
    finally:
        print("程序结束。")

if __name__ == '__main__':
    main()