{"inputs": {"0": {"name": "启动按钮", "type": "button", "alarm": false, "description": "系统启动按钮"}, "1": {"name": "停止按钮", "type": "button", "alarm": false, "description": "系统停止按钮"}, "2": {"name": "急停按钮", "type": "emergency", "alarm": true, "description": "紧急停止按钮"}, "3": {"name": "安全门", "type": "safety", "alarm": true, "description": "安全门状态检测"}, "4": {"name": "压力开关", "type": "sensor", "alarm": true, "description": "系统压力监测"}, "5": {"name": "温度开关", "type": "sensor", "alarm": true, "description": "温度过高保护"}, "6": {"name": "液位开关", "type": "sensor", "alarm": false, "description": "液位检测"}, "7": {"name": "振动传感器", "type": "sensor", "alarm": true, "description": "设备振动监测"}, "8": {"name": "光电开关1", "type": "sensor", "alarm": false, "description": "位置检测1"}, "9": {"name": "光电开关2", "type": "sensor", "alarm": false, "description": "位置检测2"}, "10": {"name": "接近开关1", "type": "sensor", "alarm": false, "description": "接近检测1"}, "11": {"name": "接近开关2", "type": "sensor", "alarm": false, "description": "接近检测2"}, "12": {"name": "限位开关1", "type": "limit", "alarm": false, "description": "行程限位1"}, "13": {"name": "限位开关2", "type": "limit", "alarm": false, "description": "行程限位2"}, "14": {"name": "备用输入1", "type": "spare", "alarm": false, "description": "备用输入通道1"}, "15": {"name": "备用输入2", "type": "spare", "alarm": false, "description": "备用输入通道2"}}, "outputs": {"0": {"name": "主电机", "type": "motor", "interlock": ["2", "3"], "description": "主驱动电机"}, "1": {"name": "冷却泵", "type": "pump", "interlock": ["2"], "description": "冷却循环泵"}, "2": {"name": "加热器", "type": "heater", "interlock": ["2", "5"], "description": "加热装置"}, "3": {"name": "风扇", "type": "fan", "interlock": ["2"], "description": "散热风扇"}, "4": {"name": "指示灯-运行", "type": "indicator", "interlock": [], "description": "运行状态指示灯(绿色)"}, "5": {"name": "指示灯-故障", "type": "indicator", "interlock": [], "description": "故障状态指示灯(红色)"}, "6": {"name": "指示灯-待机", "type": "indicator", "interlock": [], "description": "待机状态指示灯(黄色)"}, "7": {"name": "蜂鸣器", "type": "alarm", "interlock": [], "description": "报警蜂鸣器"}, "8": {"name": "电磁阀1", "type": "valve", "interlock": ["2", "3"], "description": "进料电磁阀"}, "9": {"name": "电磁阀2", "type": "valve", "interlock": ["2", "3"], "description": "排料电磁阀"}, "10": {"name": "气缸1", "type": "cylinder", "interlock": ["2", "3"], "description": "夹紧气缸"}, "11": {"name": "气缸2", "type": "cylinder", "interlock": ["2", "3"], "description": "推料气缸"}, "12": {"name": "备用输出1", "type": "spare", "interlock": [], "description": "备用输出通道1"}, "13": {"name": "备用输出2", "type": "spare", "interlock": [], "description": "备用输出通道2"}, "14": {"name": "备用输出3", "type": "spare", "interlock": [], "description": "备用输出通道3"}, "15": {"name": "备用输出4", "type": "spare", "interlock": [], "description": "备用输出通道4"}}, "scan_rate": 100, "alarm_delay": 1000, "system": {"name": "GSIO-B-EIC-1616P控制系统", "version": "1.0", "description": "基于pycomm3的EtherNet/IP IO控制系统"}}