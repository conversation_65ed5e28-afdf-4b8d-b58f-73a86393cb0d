from pycomm3 import CIPDriver, Services, ClassCode
import time

# 设备IP地址 - 请替换为您设备的实际IP地址
device_ip = '*************'  

try:
    # 建立与设备的连接
    with CIPDriver(device_ip) as device:
        print(f"已连接到设备: {device_ip}")
        
        # 读取设备信息 (可选但有助于确认连接)
        identity_data = device.generic_message(
            service=Services.get_attributes_all,
            class_code=ClassCode.identity_object,
            instance=1,
            connected=False,
            unconnected_send=True
        )
        
        if identity_data:
            print(f"设备信息: {identity_data}")
        else:
            print(f"无法读取设备信息: {identity_data.error}")
        
        # 读取输入装配实例 (Input Assembly)
        # 根据您EDS文件中的定义，输入装配实例是100
        input_data = device.generic_message(
            service=Services.get_attribute_single,
            class_code=ClassCode.assembly,  # 0x04
            instance=100,  # 从EDS文件: Assem100 = "Input Assembly"
            attribute=3,   # 通常属性3包含装配数据
            connected=False,
            unconnected_send=True
        )
        
        if input_data:
            print(f"输入数据: {input_data.value}")
        else:
            print(f"读取输入数据失败: {input_data.error}")
        
        # 写入输出装配实例 (Output Assembly)
        # 根据您EDS文件中的定义，输出装配实例是150
        # 创建一个16字节的输出数据 (基于EDS中的定义)
        output_value = bytearray(16)  # 16字节输出
        output_value[0] = 1  # 设置第一个输出位为ON
        output_value[1] = 2  # 设置第二个输出位为特定值
        
        output_result = device.generic_message(
            service=Services.set_attribute_single,
            class_code=ClassCode.assembly,  # 0x04
            instance=150,  # 从EDS文件: Assem150 = "Output Assembly"
            attribute=3,   # 通常属性3包含装配数据
            request_data=output_value,
            connected=False,
            unconnected_send=True
        )
        
        if output_result:
            print("成功写入输出数据")
        else:
            print(f"写入输出数据失败: {output_result.error}")
        
        # 可选: 建立循环轮询来持续监控I/O状态
        print("开始监控I/O状态 (按Ctrl+C退出)...")
        try:
            for i in range(5):  # 限制为5次循环示例
                # 读取输入状态
                input_state = device.generic_message(
                    service=Services.get_attribute_single,
                    class_code=ClassCode.assembly,
                    instance=100,
                    attribute=3,
                    connected=False,
                    unconnected_send=True
                )
                
                if input_state:
                    print(f"输入状态: {input_state.value}")
                else:
                    print(f"读取输入状态失败: {input_state.error}")
                
                time.sleep(1)  # 等待1秒
        except KeyboardInterrupt:
            print("监控停止")

except Exception as e:
    print(f"发生错误: {e}")