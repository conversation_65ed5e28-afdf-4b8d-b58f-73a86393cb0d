#!/usr/bin/env python3
"""
基于libplctag的GSIO-B-EIC-1616P控制程序
libplctag是专门用于工业自动化通信的库，对EtherNet/IP设备支持更好
"""

import time
import struct
from ctypes import *

try:
    from pylogix import PLC
    PYLOGIX_AVAILABLE = True
except ImportError:
    PYLOGIX_AVAILABLE = False

try:
    # 尝试导入libplctag的Python绑定
    import plctag
    LIBPLCTAG_AVAILABLE = True
except ImportError:
    LIBPLCTAG_AVAILABLE = False

class GSIOLibPLCTagSolution:
    """基于libplctag的GSIO解决方案"""
    
    def __init__(self, ip_address: str):
        self.ip_address = ip_address
        self.input_tag = None
        self.output_tag = None
        
        # 从EDS文件获取的参数
        self.INPUT_ASSEMBLY = 100   # 输入Assembly
        self.OUTPUT_ASSEMBLY = 150  # 输出Assembly
        self.INPUT_SIZE = 2         # 2字节输入
        self.OUTPUT_SIZE = 2        # 2字节输出
        
        print(f"GSIO libplctag 解决方案")
        print(f"设备IP: {self.ip_address}")
        
        if not LIBPLCTAG_AVAILABLE:
            print("警告: libplctag未安装，将使用替代方案")
    
    def connect_libplctag(self) -> bool:
        """使用libplctag连接"""
        if not LIBPLCTAG_AVAILABLE:
            return False
            
        try:
            # 创建输入标签 - Assembly 100
            input_tag_string = f"protocol=ab_eip&gateway={self.ip_address}&path=1,0&cpu=LGX&elem_size=2&elem_count=1&name=Assembly[{self.INPUT_ASSEMBLY}].Data"
            self.input_tag = plctag.Tag(input_tag_string, 1000)  # 1秒超时
            
            # 创建输出标签 - Assembly 150  
            output_tag_string = f"protocol=ab_eip&gateway={self.ip_address}&path=1,0&cpu=LGX&elem_size=2&elem_count=1&name=Assembly[{self.OUTPUT_ASSEMBLY}].Data"
            self.output_tag = plctag.Tag(output_tag_string, 1000)
            
            # 测试连接
            if self.input_tag.read() == plctag.PLCTAG_STATUS_OK:
                print("✓ libplctag连接成功")
                return True
            else:
                print(f"✗ libplctag连接失败: {self.input_tag.status}")
                return False
                
        except Exception as e:
            print(f"✗ libplctag连接异常: {e}")
            return False
    
    def connect_alternative_cip(self) -> bool:
        """使用替代的CIP连接方法"""
        try:
            # 方法1: 直接CIP Assembly访问
            input_tag_string = f"protocol=ab_eip&gateway={self.ip_address}&cpu=compactlogix&elem_size=2&elem_count=1&name=@{self.INPUT_ASSEMBLY:02X}:3"
            self.input_tag = plctag.Tag(input_tag_string, 2000)
            
            output_tag_string = f"protocol=ab_eip&gateway={self.ip_address}&cpu=compactlogix&elem_size=2&elem_count=1&name=@{self.OUTPUT_ASSEMBLY:02X}:3"
            self.output_tag = plctag.Tag(output_tag_string, 2000)
            
            if self.input_tag.read() == plctag.PLCTAG_STATUS_OK:
                print("✓ 替代CIP方法连接成功")
                return True
            else:
                print(f"✗ 替代CIP方法失败: {self.input_tag.status}")
                return False
                
        except Exception as e:
            print(f"✗ 替代CIP方法异常: {e}")
            return False
    
    def connect_generic_eip(self) -> bool:
        """使用通用EtherNet/IP方法"""
        try:
            # 通用EtherNet/IP设备连接
            base_string = f"protocol=ab_eip&gateway={self.ip_address}&path=1,0&plc=generic"
            
            # 输入Assembly标签
            input_tag_string = f"{base_string}&elem_size=2&elem_count=1&name=InputAssembly"
            self.input_tag = plctag.Tag(input_tag_string, 2000)
            
            # 输出Assembly标签  
            output_tag_string = f"{base_string}&elem_size=2&elem_count=1&name=OutputAssembly"
            self.output_tag = plctag.Tag(output_tag_string, 2000)
            
            if self.input_tag.read() == plctag.PLCTAG_STATUS_OK:
                print("✓ 通用EIP方法连接成功")
                return True
            else:
                print(f"✗ 通用EIP方法失败: {self.input_tag.status}")
                return False
                
        except Exception as e:
            print(f"✗ 通用EIP方法异常: {e}")
            return False
    
    def connect_raw_assembly(self) -> bool:
        """使用原始Assembly访问"""
        try:
            # 原始Assembly访问方法
            input_tag_string = f"protocol=ab_eip&gateway={self.ip_address}&elem_size=2&elem_count=1&name=Class[0x04].Instance[{self.INPUT_ASSEMBLY}].Attribute[3]"
            self.input_tag = plctag.Tag(input_tag_string, 2000)
            
            output_tag_string = f"protocol=ab_eip&gateway={self.ip_address}&elem_size=2&elem_count=1&name=Class[0x04].Instance[{self.OUTPUT_ASSEMBLY}].Attribute[3]"
            self.output_tag = plctag.Tag(output_tag_string, 2000)
            
            if self.input_tag.read() == plctag.PLCTAG_STATUS_OK:
                print("✓ 原始Assembly方法连接成功")
                return True
            else:
                print(f"✗ 原始Assembly方法失败: {self.input_tag.status}")
                return False
                
        except Exception as e:
            print(f"✗ 原始Assembly方法异常: {e}")
            return False
    
    def connect(self) -> bool:
        """尝试多种连接方法"""
        print(f"\n连接到设备: {self.ip_address}")
        
        if not LIBPLCTAG_AVAILABLE:
            print("libplctag不可用，请安装: pip install pylogix 或 pip install libplctag")
            return False
        
        # 按优先级尝试不同连接方法
        connection_methods = [
            ("标准libplctag", self.connect_libplctag),
            ("替代CIP方法", self.connect_alternative_cip),
            ("通用EIP方法", self.connect_generic_eip),
            ("原始Assembly", self.connect_raw_assembly)
        ]
        
        for method_name, method_func in connection_methods:
            print(f"\n--- 尝试{method_name} ---")
            if method_func():
                return True
            time.sleep(1)
        
        print("✗ 所有连接方法都失败")
        return False
    
    def read_inputs(self) -> int:
        """读取输入状态"""
        if not self.input_tag:
            return None
            
        try:
            status = self.input_tag.read()
            if status == plctag.PLCTAG_STATUS_OK:
                # 读取2字节数据
                value = self.input_tag.get_uint16(0)
                return value
            else:
                print(f"读取失败: 状态码 {status}")
                return None
                
        except Exception as e:
            print(f"读取异常: {e}")
            return None
    
    def write_outputs(self, output_value: int) -> bool:
        """写入输出状态"""
        if not self.output_tag:
            print("输出标签未初始化")
            return False
            
        try:
            # 设置2字节数据
            self.output_tag.set_uint16(0, output_value)
            
            # 写入到设备
            status = self.output_tag.write()
            
            if status == plctag.PLCTAG_STATUS_OK:
                print(f"✓ 写入成功: 0x{output_value:04X}")
                return True
            else:
                print(f"✗ 写入失败: 状态码 {status}")
                return False
                
        except Exception as e:
            print(f"✗ 写入异常: {e}")
            return False
    
    def test_io_comprehensive(self):
        """综合I/O测试"""
        print("\n开始综合I/O测试...")
        
        # 测试模式
        test_values = [
            (0x0001, "输出0"),
            (0x0002, "输出1"), 
            (0x0004, "输出2"),
            (0x0008, "输出3"),
            (0x00FF, "输出0-7"),
            (0xFF00, "输出8-15"),
            (0xAAAA, "交替模式"),
            (0x5555, "反向交替"),
            (0xFFFF, "全部输出"),
            (0x0000, "清除输出")
        ]
        
        for value, description in test_values:
            print(f"\n{'='*50}")
            print(f"测试: {description} (0x{value:04X})")
            print(f"{'='*50}")
            
            # 写入输出
            success = self.write_outputs(value)
            
            if success:
                # 等待一下让输出稳定
                time.sleep(0.5)
                
                # 读取输入验证
                inputs = self.read_inputs()
                if inputs is not None:
                    print(f"当前输入: 0x{inputs:04X}")
                    
                    # 显示位状态
                    print("输出状态:")
                    for i in range(16):
                        state = "ON " if (value & (1 << i)) else "OFF"
                        print(f"  输出{i:2d}: {state}")
                
                print("等待3秒...")
                time.sleep(3)
            else:
                print("写入失败，跳过此测试")
    
    def disconnect(self):
        """断开连接"""
        try:
            if self.input_tag:
                self.input_tag.close()
            if self.output_tag:
                self.output_tag.close()
        except:
            pass
        
        self.input_tag = None
        self.output_tag = None
        print("已断开连接")

class GSIOPyLogixSolution:
    """使用PyLogix作为备选方案"""
    
    def __init__(self, ip_address: str):
        self.ip_address = ip_address
        self.plc = None
        
        print(f"GSIO PyLogix 备选方案")
        print(f"设备IP: {self.ip_address}")
    
    def connect(self) -> bool:
        """连接到设备"""
        if not PYLOGIX_AVAILABLE:
            print("PyLogix不可用，请安装: pip install pylogix")
            return False
            
        try:
            self.plc = PLC()
            self.plc.IPAddress = self.ip_address
            
            # 测试连接
            result = self.plc.Read("Assembly[100].Data", 1)
            if result.Status == "Success":
                print("✓ PyLogix连接成功")
                return True
            else:
                print(f"✗ PyLogix连接失败: {result.Status}")
                return False
                
        except Exception as e:
            print(f"✗ PyLogix连接异常: {e}")
            return False
    
    def read_inputs(self) -> int:
        """读取输入"""
        if not self.plc:
            return None
            
        try:
            result = self.plc.Read("Assembly[100].Data", 1)
            if result.Status == "Success":
                return result.Value
            else:
                print(f"读取失败: {result.Status}")
                return None
                
        except Exception as e:
            print(f"读取异常: {e}")
            return None
    
    def write_outputs(self, output_value: int) -> bool:
        """写入输出"""
        if not self.plc:
            return False
            
        try:
            result = self.plc.Write("Assembly[150].Data", output_value)
            if result.Status == "Success":
                print(f"✓ 写入成功: 0x{output_value:04X}")
                return True
            else:
                print(f"✗ 写入失败: {result.Status}")
                return False
                
        except Exception as e:
            print(f"✗ 写入异常: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.plc:
            self.plc.Close()
            self.plc = None
        print("已断开连接")

def install_dependencies():
    """安装依赖"""
    print("检查并安装依赖...")
    
    import subprocess
    import sys
    
    dependencies = [
        ("libplctag", "libplctag"),
        ("pylogix", "pylogix")
    ]
    
    for package_name, import_name in dependencies:
        try:
            __import__(import_name)
            print(f"✓ {package_name} 已安装")
        except ImportError:
            print(f"安装 {package_name}...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
                print(f"✓ {package_name} 安装成功")
            except subprocess.CalledProcessError:
                print(f"✗ {package_name} 安装失败")

def main():
    print("=" * 60)
    print("    基于libplctag的GSIO控制程序")
    print("    专业工业自动化通信库")
    print("=" * 60)
    
    # 检查依赖
    if not LIBPLCTAG_AVAILABLE and not PYLOGIX_AVAILABLE:
        print("未检测到libplctag或pylogix，是否安装？(y/n): ", end="")
        if input().lower() == 'y':
            install_dependencies()
            print("请重新运行程序")
            return
    
    ip_address = input("请输入设备IP地址 (默认***************): ").strip()
    if not ip_address:
        ip_address = "***************"
    
    # 优先使用libplctag
    if LIBPLCTAG_AVAILABLE:
        controller = GSIOLibPLCTagSolution(ip_address)
    elif PYLOGIX_AVAILABLE:
        controller = GSIOPyLogixSolution(ip_address)
    else:
        print("没有可用的通信库")
        return
    
    if not controller.connect():
        print("连接失败")
        return
    
    try:
        while True:
            print("\n" + "=" * 50)
            print("选择操作:")
            print("1. 读取输入状态")
            print("2. 写入输出状态")
            print("3. 综合I/O测试")
            print("0. 退出")
            print("=" * 50)
            
            choice = input("请选择 (0-3): ").strip()
            
            if choice == "1":
                inputs = controller.read_inputs()
                if inputs is not None:
                    print(f"\n输入状态: 0x{inputs:04X} ({inputs:016b})")
                    print("详细状态:")
                    for i in range(16):
                        state = "ON " if (inputs & (1 << i)) else "OFF"
                        print(f"  输入{i:2d}: {state}")
            
            elif choice == "2":
                try:
                    output_str = input("请输入输出值 (十六进制，如 0x0001): ").strip()
                    if output_str.startswith('0x'):
                        output_value = int(output_str, 16)
                    else:
                        output_value = int(output_str)
                    
                    controller.write_outputs(output_value)
                    
                except ValueError:
                    print("输入格式错误")
            
            elif choice == "3":
                if hasattr(controller, 'test_io_comprehensive'):
                    controller.test_io_comprehensive()
                else:
                    print("此方案不支持综合测试")
            
            elif choice == "0":
                break
            
            else:
                print("无效选择")
    
    finally:
        controller.disconnect()

if __name__ == "__main__":
    main()
