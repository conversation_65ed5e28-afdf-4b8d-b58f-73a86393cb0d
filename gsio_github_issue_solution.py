#!/usr/bin/env python3
"""
基于GitHub Issue #279的GSIO解决方案
参考：https://github.com/ottowayi/pycomm3/issues/279
解决Assembly写入的"Too Much Data"和"Insufficient Command Data"问题
"""

import time
import struct
from pycomm3 import CIPDriver, Services, ClassCode, UINT, BYTE

class GSIOGitHubSolution:
    """基于GitHub Issue #279的解决方案"""
    
    def __init__(self, ip_address: str):
        self.ip_address = ip_address
        self.driver = None
        
        # 从EDS和手册获取的参数
        self.INPUT_ASSEMBLY = 100   # 输入Assembly (0x64)
        self.OUTPUT_ASSEMBLY = 150  # 输出Assembly (0x96)
        self.INPUT_SIZE = 2         # 2字节输入
        self.OUTPUT_SIZE = 2        # 2字节输出
        
        print(f"GSIO GitHub Issue #279 解决方案")
        print(f"设备IP: {self.ip_address}")
    
    def connect(self) -> bool:
        """连接到设备"""
        print(f"\n连接到设备: {self.ip_address}")
        
        try:
            self.driver = CIPDriver(self.ip_address)
            self.driver.open()
            print("✓ 连接成功")
            return True
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            return False
    
    def read_inputs(self) -> int:
        """读取输入（已知可用的方法）"""
        if not self.driver:
            return None
            
        try:
            result = self.driver.generic_message(
                service=Services.get_attribute_single,
                class_code=ClassCode.assembly,
                instance=self.INPUT_ASSEMBLY,
                attribute=3,
                data_type=UINT,
                connected=True,
                unconnected_send=False,
                route_path=False,
                name='read_inputs'
            )
            
            if result and hasattr(result, 'value'):
                return result.value
            else:
                print(f"读取失败: {result.error if result else '无响应'}")
                return None
                
        except Exception as e:
            print(f"读取异常: {e}")
            return None
    
    def write_outputs_method1_raw_bytes(self, output_value: int) -> bool:
        """方法1: 使用原始字节数据（基于GitHub Issue #279）"""
        try:
            # 将16位值转换为2字节数据（小端序）
            vb = struct.pack('<H', output_value)
            print(f"发送数据长度: {len(vb)} 字节")
            print(f"发送数据: {vb.hex()}")
            
            result = self.driver.generic_message(
                service=0x10,  # Set_Attribute_Single (原始字节)
                class_code=0x04,  # Assembly Class
                instance=self.OUTPUT_ASSEMBLY,  # 150 = 0x96
                attribute=0x03,
                data_type=BYTE[2],  # 明确指定2字节
                connected=False,
                request_data=vb,
                name='write_method1_raw'
            )
            
            if result:
                print(f"✓ 方法1成功: 0x{output_value:04X}")
                return True
            else:
                print(f"✗ 方法1失败: {result.error if result else '无响应'}")
                return False
                
        except Exception as e:
            print(f"✗ 方法1异常: {e}")
            return False
    
    def write_outputs_method2_no_data_type(self, output_value: int) -> bool:
        """方法2: 不指定data_type（避免额外的数据包装）"""
        try:
            vb = struct.pack('<H', output_value)
            
            result = self.driver.generic_message(
                service=Services.set_attribute_single,
                class_code=ClassCode.assembly,
                instance=self.OUTPUT_ASSEMBLY,
                attribute=3,
                connected=False,
                request_data=vb,
                # 不指定data_type
                name='write_method2_no_dtype'
            )
            
            if result:
                print(f"✓ 方法2成功: 0x{output_value:04X}")
                return True
            else:
                print(f"✗ 方法2失败: {result.error if result else '无响应'}")
                return False
                
        except Exception as e:
            print(f"✗ 方法2异常: {e}")
            return False
    
    def write_outputs_method3_connected_mode(self, output_value: int) -> bool:
        """方法3: 使用连接模式（与读取保持一致）"""
        try:
            vb = struct.pack('<H', output_value)
            
            result = self.driver.generic_message(
                service=Services.set_attribute_single,
                class_code=ClassCode.assembly,
                instance=self.OUTPUT_ASSEMBLY,
                attribute=3,
                connected=True,  # 与读取保持一致
                unconnected_send=False,
                route_path=False,
                request_data=vb,
                name='write_method3_connected'
            )
            
            if result:
                print(f"✓ 方法3成功: 0x{output_value:04X}")
                return True
            else:
                print(f"✗ 方法3失败: {result.error if result else '无响应'}")
                return False
                
        except Exception as e:
            print(f"✗ 方法3异常: {e}")
            return False
    
    def write_outputs_method4_byte_array(self, output_value: int) -> bool:
        """方法4: 使用字节数组格式"""
        try:
            # 分解为两个字节
            low_byte = output_value & 0xFF
            high_byte = (output_value >> 8) & 0xFF
            vb = bytes([low_byte, high_byte])
            
            result = self.driver.generic_message(
                service=Services.set_attribute_single,
                class_code=ClassCode.assembly,
                instance=self.OUTPUT_ASSEMBLY,
                attribute=3,
                connected=False,
                request_data=vb,
                data_type=BYTE[2],
                name='write_method4_byte_array'
            )
            
            if result:
                print(f"✓ 方法4成功: 0x{output_value:04X}")
                return True
            else:
                print(f"✗ 方法4失败: {result.error if result else '无响应'}")
                return False
                
        except Exception as e:
            print(f"✗ 方法4异常: {e}")
            return False
    
    def write_outputs_method5_minimal(self, output_value: int) -> bool:
        """方法5: 最小化参数（参考GitHub Issue中的成功案例）"""
        try:
            vb = struct.pack('<H', output_value)
            
            result = self.driver.generic_message(
                service=b"\x10",  # 原始服务码
                class_code=0x04,  # Assembly Class
                instance=0x96,    # 150的十六进制
                attribute=0x03,
                request_data=vb,
                connected=False
            )
            
            if result:
                print(f"✓ 方法5成功: 0x{output_value:04X}")
                return True
            else:
                print(f"✗ 方法5失败: {result.error if result else '无响应'}")
                return False
                
        except Exception as e:
            print(f"✗ 方法5异常: {e}")
            return False
    
    def write_outputs(self, output_value: int) -> bool:
        """写入输出（尝试所有方法）"""
        if not self.driver:
            print("设备未连接")
            return False
        
        print(f"\n尝试写入输出: 0x{output_value:04X} ({output_value})")
        
        # 按顺序尝试不同方法
        methods = [
            ("原始字节数据", self.write_outputs_method1_raw_bytes),
            ("不指定data_type", self.write_outputs_method2_no_data_type),
            ("连接模式", self.write_outputs_method3_connected_mode),
            ("字节数组", self.write_outputs_method4_byte_array),
            ("最小化参数", self.write_outputs_method5_minimal)
        ]
        
        for method_name, method_func in methods:
            print(f"\n--- 尝试{method_name} ---")
            if method_func(output_value):
                return True
            time.sleep(0.5)  # 方法间延迟
        
        print("✗ 所有写入方法都失败")
        return False
    
    def test_io_comprehensive(self):
        """综合I/O测试"""
        print("\n开始综合I/O测试...")
        
        # 测试模式
        test_values = [
            (0x0001, "输出0"),
            (0x0002, "输出1"), 
            (0x0004, "输出2"),
            (0x0008, "输出3"),
            (0x00FF, "输出0-7"),
            (0xFF00, "输出8-15"),
            (0xAAAA, "交替模式"),
            (0x5555, "反向交替"),
            (0xFFFF, "全部输出"),
            (0x0000, "清除输出")
        ]
        
        for value, description in test_values:
            print(f"\n{'='*50}")
            print(f"测试: {description} (0x{value:04X})")
            print(f"{'='*50}")
            
            # 写入输出
            success = self.write_outputs(value)
            
            if success:
                # 等待一下让输出稳定
                time.sleep(0.5)
                
                # 读取输入验证
                inputs = self.read_inputs()
                if inputs is not None:
                    print(f"当前输入: 0x{inputs:04X}")
                    
                    # 显示位状态
                    print("输出状态:")
                    for i in range(16):
                        state = "ON " if (value & (1 << i)) else "OFF"
                        print(f"  输出{i:2d}: {state}")
                
                print("等待3秒...")
                time.sleep(3)
            else:
                print("写入失败，跳过此测试")
    
    def disconnect(self):
        """断开连接"""
        if self.driver:
            try:
                self.driver.close()
            except:
                pass
            self.driver = None
            print("已断开连接")

def main():
    print("=" * 60)
    print("    基于GitHub Issue #279的GSIO解决方案")
    print("    解决Assembly写入问题")
    print("=" * 60)
    
    ip_address = input("请输入设备IP地址 (默认***************): ").strip()
    if not ip_address:
        ip_address = "***************"
    
    controller = GSIOGitHubSolution(ip_address)
    
    if not controller.connect():
        print("连接失败")
        return
    
    try:
        while True:
            print("\n" + "=" * 50)
            print("选择操作:")
            print("1. 读取输入状态")
            print("2. 写入输出状态")
            print("3. 综合I/O测试")
            print("0. 退出")
            print("=" * 50)
            
            choice = input("请选择 (0-3): ").strip()
            
            if choice == "1":
                inputs = controller.read_inputs()
                if inputs is not None:
                    print(f"\n输入状态: 0x{inputs:04X} ({inputs:016b})")
                    print("详细状态:")
                    for i in range(16):
                        state = "ON " if (inputs & (1 << i)) else "OFF"
                        print(f"  输入{i:2d}: {state}")
            
            elif choice == "2":
                try:
                    output_str = input("请输入输出值 (十六进制，如 0x0001): ").strip()
                    if output_str.startswith('0x'):
                        output_value = int(output_str, 16)
                    else:
                        output_value = int(output_str)
                    
                    controller.write_outputs(output_value)
                    
                except ValueError:
                    print("输入格式错误")
            
            elif choice == "3":
                controller.test_io_comprehensive()
            
            elif choice == "0":
                break
            
            else:
                print("无效选择")
    
    finally:
        controller.disconnect()

if __name__ == "__main__":
    main()
