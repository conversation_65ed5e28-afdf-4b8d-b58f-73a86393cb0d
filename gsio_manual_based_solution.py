#!/usr/bin/env python3
"""
基于产品手册的GSIO-B-EIC-1616P完整解决方案
根据官方手册和EDS文件创建的正确实现
"""

import time
import struct
import requests
from pycomm3 import CIPDriver, Services, ClassCode, UINT

class GSIOManualController:
    """基于产品手册的GSIO控制器"""
    
    def __init__(self, communication_ip: str = "***************"):
        """
        初始化控制器
        Args:
            communication_ip: 与CPU通讯的IP地址（默认***************）
        """
        self.communication_ip = communication_ip
        self.config_ip = "*************"  # 固定的配置IP
        self.driver = None
        
        # 从手册获取的参数
        self.INPUT_ASSEMBLY = 100   # 输入Assembly
        self.OUTPUT_ASSEMBLY = 150  # 输出Assembly
        self.INPUT_SIZE = 2         # 2字节输入
        self.OUTPUT_SIZE = 2        # 2字节输出
        
        print(f"GSIO-B-EIC-1616P 控制器初始化")
        print(f"通讯IP: {self.communication_ip}")
        print(f"配置IP: {self.config_ip}")
    
    def check_device_config(self) -> bool:
        """检查设备配置"""
        print("\n检查设备配置...")
        
        try:
            # 尝试访问配置网页
            config_url = f"http://{self.config_ip}:2250"
            print(f"尝试访问配置页面: {config_url}")
            
            response = requests.get(config_url, timeout=5)
            if response.status_code == 200:
                print("✓ 设备配置页面可访问")
                return True
            else:
                print(f"✗ 配置页面访问失败: {response.status_code}")
                
        except Exception as e:
            print(f"✗ 无法访问配置页面: {e}")
        
        return False
    
    def connect(self) -> bool:
        """连接到设备"""
        print(f"\n连接到GSIO设备: {self.communication_ip}")
        
        try:
            # 创建CIP驱动
            self.driver = CIPDriver(self.communication_ip)
            
            # 尝试连接
            print("建立CIP连接...")
            self.driver.open()
            
            print("✓ CIP连接成功")
            
            # 测试读取输入
            print("测试读取输入...")
            inputs = self.read_inputs()
            if inputs is not None:
                print(f"✓ 输入读取成功: 0x{inputs:04X}")
                return True
            else:
                print("✗ 输入读取失败")
                return False
                
        except Exception as e:
            print(f"✗ 连接失败: {e}")
            return False
    
    def read_inputs(self) -> int:
        """读取输入状态"""
        if not self.driver:
            return None
            
        try:
            # 使用已知可用的方法
            result = self.driver.generic_message(
                service=Services.get_attribute_single,
                class_code=ClassCode.assembly,
                instance=self.INPUT_ASSEMBLY,
                attribute=3,
                data_type=UINT,
                connected=True,
                unconnected_send=False,
                route_path=False,
                name='read_inputs'
            )
            
            if result and hasattr(result, 'value'):
                return result.value
            else:
                print(f"读取失败: {result.error if result else '无响应'}")
                return None
                
        except Exception as e:
            print(f"读取异常: {e}")
            return None
    
    def write_outputs_method1(self, output_value: int) -> bool:
        """方法1: 使用set_attribute_single写入输出"""
        try:
            result = self.driver.generic_message(
                service=Services.set_attribute_single,
                class_code=ClassCode.assembly,
                instance=self.OUTPUT_ASSEMBLY,
                attribute=3,
                request_data=UINT.encode(output_value),
                connected=True,
                unconnected_send=False,
                route_path=False,
                name='write_outputs_method1'
            )
            
            if result:
                print(f"✓ 方法1写入成功: 0x{output_value:04X}")
                return True
            else:
                print(f"✗ 方法1写入失败: {result.error if result else '无响应'}")
                return False
                
        except Exception as e:
            print(f"✗ 方法1异常: {e}")
            return False
    
    def write_outputs_method2(self, output_value: int) -> bool:
        """方法2: 使用原始字节数据写入"""
        try:
            # 将16位值转换为2字节数据
            data = struct.pack('<H', output_value)
            
            result = self.driver.generic_message(
                service=Services.set_attribute_single,
                class_code=ClassCode.assembly,
                instance=self.OUTPUT_ASSEMBLY,
                attribute=3,
                request_data=data,
                connected=True,
                unconnected_send=False,
                route_path=False,
                name='write_outputs_method2'
            )
            
            if result:
                print(f"✓ 方法2写入成功: 0x{output_value:04X}")
                return True
            else:
                print(f"✗ 方法2写入失败: {result.error if result else '无响应'}")
                return False
                
        except Exception as e:
            print(f"✗ 方法2异常: {e}")
            return False
    
    def write_outputs_method3(self, output_value: int) -> bool:
        """方法3: 使用不同的连接参数"""
        try:
            result = self.driver.generic_message(
                service=Services.set_attribute_single,
                class_code=ClassCode.assembly,
                instance=self.OUTPUT_ASSEMBLY,
                attribute=3,
                request_data=UINT.encode(output_value),
                connected=False,  # 尝试非连接模式
                unconnected_send=True,
                route_path=False,
                name='write_outputs_method3'
            )
            
            if result:
                print(f"✓ 方法3写入成功: 0x{output_value:04X}")
                return True
            else:
                print(f"✗ 方法3写入失败: {result.error if result else '无响应'}")
                return False
                
        except Exception as e:
            print(f"✗ 方法3异常: {e}")
            return False
    
    def write_outputs_method4(self, output_value: int) -> bool:
        """方法4: 尝试不同的Assembly实例"""
        try:
            # 尝试不同的实例号
            for instance in [150, 151, 152]:
                print(f"尝试实例 {instance}...")
                result = self.driver.generic_message(
                    service=Services.set_attribute_single,
                    class_code=ClassCode.assembly,
                    instance=instance,
                    attribute=3,
                    request_data=UINT.encode(output_value),
                    connected=True,
                    unconnected_send=False,
                    route_path=False,
                    name=f'write_outputs_instance_{instance}'
                )
                
                if result:
                    print(f"✓ 方法4写入成功 (实例{instance}): 0x{output_value:04X}")
                    self.OUTPUT_ASSEMBLY = instance  # 更新成功的实例
                    return True
                else:
                    print(f"✗ 实例{instance}写入失败: {result.error if result else '无响应'}")
            
            return False
                
        except Exception as e:
            print(f"✗ 方法4异常: {e}")
            return False
    
    def write_outputs(self, output_value: int) -> bool:
        """写入输出（尝试多种方法）"""
        if not self.driver:
            print("设备未连接")
            return False
        
        print(f"\n尝试写入输出: 0x{output_value:04X}")
        
        # 按顺序尝试不同方法
        methods = [
            self.write_outputs_method1,
            self.write_outputs_method2,
            self.write_outputs_method3,
            self.write_outputs_method4
        ]
        
        for i, method in enumerate(methods, 1):
            print(f"\n--- 尝试方法{i} ---")
            if method(output_value):
                return True
            time.sleep(0.5)  # 方法间延迟
        
        print("✗ 所有写入方法都失败")
        return False
    
    def test_io_pattern(self):
        """测试I/O模式"""
        print("\n开始I/O测试模式...")
        
        # 测试模式
        patterns = [
            (0x0001, "输出0"),
            (0x0002, "输出1"), 
            (0x0004, "输出2"),
            (0x0008, "输出3"),
            (0x00FF, "输出0-7"),
            (0xFF00, "输出8-15"),
            (0xFFFF, "全部输出"),
            (0x0000, "清除输出")
        ]
        
        for pattern, description in patterns:
            print(f"\n设置 {description}: 0x{pattern:04X}")
            
            # 写入输出
            if self.write_outputs(pattern):
                # 读取输入验证
                time.sleep(0.5)
                inputs = self.read_inputs()
                if inputs is not None:
                    print(f"当前输入: 0x{inputs:04X}")
                    
                    # 显示位状态
                    print("输出状态:")
                    for i in range(16):
                        state = "ON " if (pattern & (1 << i)) else "OFF"
                        print(f"  输出{i:2d}: {state}")
            
            time.sleep(2)  # 每个模式间延迟
    
    def monitor_inputs(self, duration: int = 30):
        """监控输入变化"""
        print(f"\n监控输入变化 {duration}秒...")
        
        last_inputs = None
        start_time = time.time()
        
        try:
            while time.time() - start_time < duration:
                inputs = self.read_inputs()
                
                if inputs is not None and inputs != last_inputs:
                    print(f"\n输入变化: 0x{inputs:04X} ({inputs:016b})")
                    
                    if last_inputs is not None:
                        changed = inputs ^ last_inputs
                        for i in range(16):
                            if changed & (1 << i):
                                state = "ON " if (inputs & (1 << i)) else "OFF"
                                print(f"  输入{i:2d}: {state}")
                    
                    last_inputs = inputs
                
                time.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n监控停止")
    
    def disconnect(self):
        """断开连接"""
        if self.driver:
            try:
                self.driver.close()
            except:
                pass
            self.driver = None
            print("已断开连接")

def main():
    print("=" * 60)
    print("    基于产品手册的GSIO-B-EIC-1616P解决方案")
    print("=" * 60)
    
    # 获取通讯IP
    comm_ip = input("请输入设备通讯IP地址 (默认***************): ").strip()
    if not comm_ip:
        comm_ip = "***************"
    
    controller = GSIOManualController(comm_ip)
    
    # 检查设备配置
    controller.check_device_config()
    
    # 连接设备
    if not controller.connect():
        print("连接失败，请检查:")
        print("1. 设备IP地址是否正确")
        print("2. 网络连接是否正常")
        print("3. 设备是否已通电")
        return
    
    try:
        while True:
            print("\n" + "=" * 50)
            print("选择操作:")
            print("1. 读取输入状态")
            print("2. 写入输出状态")
            print("3. I/O测试模式")
            print("4. 监控输入变化")
            print("5. 检查设备配置")
            print("0. 退出")
            print("=" * 50)
            
            choice = input("请选择 (0-5): ").strip()
            
            if choice == "1":
                inputs = controller.read_inputs()
                if inputs is not None:
                    print(f"\n输入状态: 0x{inputs:04X} ({inputs:016b})")
                    print("详细状态:")
                    for i in range(16):
                        state = "ON " if (inputs & (1 << i)) else "OFF"
                        print(f"  输入{i:2d}: {state}")
            
            elif choice == "2":
                try:
                    output_str = input("请输入输出值 (十六进制，如 0x0001): ").strip()
                    if output_str.startswith('0x'):
                        output_value = int(output_str, 16)
                    else:
                        output_value = int(output_str)
                    
                    controller.write_outputs(output_value)
                    
                except ValueError:
                    print("输入格式错误")
            
            elif choice == "3":
                controller.test_io_pattern()
            
            elif choice == "4":
                duration = input("监控时长(秒，默认30): ").strip()
                try:
                    duration = int(duration) if duration else 30
                except:
                    duration = 30
                controller.monitor_inputs(duration)
            
            elif choice == "5":
                controller.check_device_config()
            
            elif choice == "0":
                break
            
            else:
                print("无效选择")
    
    finally:
        controller.disconnect()

if __name__ == "__main__":
    main()
