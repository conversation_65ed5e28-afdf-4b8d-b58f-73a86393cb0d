#!/usr/bin/env python3
"""
GSIO-B-EIC-1616P 工业应用示例
展示实际工业场景中的IO控制应用
"""

import time
import json
import threading
from datetime import datetime
from gsio_eip_controller import GSIOController
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('gsio_log.txt'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class IndustrialIOController:
    """工业IO控制器 - 基于GSIO-B-EIC-1616P"""
    
    def __init__(self, device_ip: str, config_file: str = "io_config.json"):
        """
        初始化工业IO控制器
        
        Args:
            device_ip: 设备IP地址
            config_file: IO配置文件路径
        """
        self.gsio = GSIOController(device_ip)
        self.config_file = config_file
        self.io_config = self.load_config()
        
        # 运行状态
        self.running = False
        self.monitor_thread = None
        
        # 数据记录
        self.input_history = []
        self.output_history = []
        
        # 报警状态
        self.alarms = {}
        
    def load_config(self) -> dict:
        """加载IO配置"""
        default_config = {
            "inputs": {
                "0": {"name": "启动按钮", "type": "button", "alarm": False},
                "1": {"name": "停止按钮", "type": "button", "alarm": False},
                "2": {"name": "急停按钮", "type": "emergency", "alarm": True},
                "3": {"name": "安全门", "type": "safety", "alarm": True},
                "4": {"name": "压力开关", "type": "sensor", "alarm": True},
                "5": {"name": "温度开关", "type": "sensor", "alarm": True},
                "6": {"name": "液位开关", "type": "sensor", "alarm": False},
                "7": {"name": "振动传感器", "type": "sensor", "alarm": True},
                "8": {"name": "光电开关1", "type": "sensor", "alarm": False},
                "9": {"name": "光电开关2", "type": "sensor", "alarm": False},
                "10": {"name": "接近开关1", "type": "sensor", "alarm": False},
                "11": {"name": "接近开关2", "type": "sensor", "alarm": False},
                "12": {"name": "限位开关1", "type": "limit", "alarm": False},
                "13": {"name": "限位开关2", "type": "limit", "alarm": False},
                "14": {"name": "备用输入1", "type": "spare", "alarm": False},
                "15": {"name": "备用输入2", "type": "spare", "alarm": False}
            },
            "outputs": {
                "0": {"name": "主电机", "type": "motor", "interlock": ["2", "3"]},
                "1": {"name": "冷却泵", "type": "pump", "interlock": ["2"]},
                "2": {"name": "加热器", "type": "heater", "interlock": ["2", "5"]},
                "3": {"name": "风扇", "type": "fan", "interlock": ["2"]},
                "4": {"name": "指示灯-运行", "type": "indicator", "interlock": []},
                "5": {"name": "指示灯-故障", "type": "indicator", "interlock": []},
                "6": {"name": "指示灯-待机", "type": "indicator", "interlock": []},
                "7": {"name": "蜂鸣器", "type": "alarm", "interlock": []},
                "8": {"name": "电磁阀1", "type": "valve", "interlock": ["2", "3"]},
                "9": {"name": "电磁阀2", "type": "valve", "interlock": ["2", "3"]},
                "10": {"name": "气缸1", "type": "cylinder", "interlock": ["2", "3"]},
                "11": {"name": "气缸2", "type": "cylinder", "interlock": ["2", "3"]},
                "12": {"name": "备用输出1", "type": "spare", "interlock": []},
                "13": {"name": "备用输出2", "type": "spare", "interlock": []},
                "14": {"name": "备用输出3", "type": "spare", "interlock": []},
                "15": {"name": "备用输出4", "type": "spare", "interlock": []}
            },
            "scan_rate": 100,  # 扫描周期(ms)
            "alarm_delay": 1000  # 报警延时(ms)
        }
        
        try:
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
                logger.info(f"加载配置文件: {self.config_file}")
                return config
        except FileNotFoundError:
            logger.warning(f"配置文件不存在，创建默认配置: {self.config_file}")
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(default_config, f, indent=2, ensure_ascii=False)
            return default_config
        except Exception as e:
            logger.error(f"加载配置文件失败: {e}")
            return default_config
    
    def start_monitoring(self):
        """启动监控"""
        if not self.gsio.connect():
            logger.error("连接设备失败")
            return False
            
        self.running = True
        self.monitor_thread = threading.Thread(target=self._monitor_loop)
        self.monitor_thread.daemon = True
        self.monitor_thread.start()
        
        logger.info("开始监控IO状态")
        return True
    
    def stop_monitoring(self):
        """停止监控"""
        self.running = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2)
        
        # 关闭所有输出
        self.gsio.write_outputs(0)
        self.gsio.disconnect()
        
        logger.info("停止监控")
    
    def _monitor_loop(self):
        """监控循环"""
        last_inputs = None
        scan_interval = self.io_config.get("scan_rate", 100) / 1000.0
        
        while self.running:
            try:
                # 读取输入状态
                current_inputs = self.gsio.read_inputs()
                if current_inputs is not None:
                    # 检查输入变化
                    if last_inputs != current_inputs:
                        self._process_input_changes(last_inputs, current_inputs)
                        last_inputs = current_inputs
                    
                    # 处理安全联锁
                    self._process_safety_interlocks(current_inputs)
                    
                    # 记录数据
                    self._record_data(current_inputs)
                
                time.sleep(scan_interval)
                
            except Exception as e:
                logger.error(f"监控循环异常: {e}")
                time.sleep(1)
    
    def _process_input_changes(self, last_inputs, current_inputs):
        """处理输入变化"""
        if last_inputs is None:
            return
            
        changed = last_inputs ^ current_inputs
        
        for bit in range(16):
            if changed & (1 << bit):
                bit_state = bool(current_inputs & (1 << bit))
                input_config = self.io_config["inputs"].get(str(bit), {})
                input_name = input_config.get("name", f"输入{bit}")
                
                logger.info(f"{input_name} 状态变化: {'ON' if bit_state else 'OFF'}")
                
                # 处理特殊输入
                self._handle_special_input(bit, bit_state, input_config)
    
    def _handle_special_input(self, bit, state, config):
        """处理特殊输入"""
        input_type = config.get("type", "")
        
        if input_type == "emergency" and state:
            # 急停处理
            logger.warning("急停按钮被按下！")
            self.emergency_stop()
            
        elif input_type == "safety" and not state:
            # 安全门打开
            logger.warning("安全门被打开！")
            self.safety_stop()
            
        elif input_type == "button":
            if bit == 0 and state:  # 启动按钮
                self.start_sequence()
            elif bit == 1 and state:  # 停止按钮
                self.stop_sequence()
    
    def _process_safety_interlocks(self, inputs):
        """处理安全联锁"""
        # 检查急停和安全门状态
        emergency_stop = bool(inputs & (1 << 2))  # 急停按钮
        safety_door = bool(inputs & (1 << 3))     # 安全门
        
        if emergency_stop or not safety_door:
            # 关闭有联锁要求的输出
            current_outputs = self.gsio.current_output_data
            
            for bit_str, output_config in self.io_config["outputs"].items():
                bit = int(bit_str)
                interlocks = output_config.get("interlock", [])
                
                if "2" in interlocks or "3" in interlocks:  # 有急停或安全门联锁
                    if current_outputs & (1 << bit):
                        self.gsio.set_output_bit(bit, False)
                        logger.warning(f"安全联锁: 关闭{output_config.get('name', f'输出{bit}')}")
    
    def _record_data(self, inputs):
        """记录数据"""
        timestamp = datetime.now().isoformat()
        
        # 记录输入状态变化
        if not self.input_history or self.input_history[-1]["value"] != inputs:
            self.input_history.append({
                "timestamp": timestamp,
                "value": inputs
            })
            
            # 限制历史记录长度
            if len(self.input_history) > 1000:
                self.input_history = self.input_history[-500:]
    
    def emergency_stop(self):
        """急停处理"""
        logger.critical("执行急停程序")
        
        # 关闭所有输出
        self.gsio.write_outputs(0)
        
        # 点亮故障指示灯和蜂鸣器
        self.gsio.set_output_bit(5, True)   # 故障指示灯
        self.gsio.set_output_bit(7, True)   # 蜂鸣器
        
        # 记录报警
        self.alarms["emergency_stop"] = {
            "timestamp": datetime.now().isoformat(),
            "message": "急停按钮被按下",
            "level": "critical"
        }
    
    def safety_stop(self):
        """安全停止"""
        logger.warning("执行安全停止程序")
        
        # 关闭主要设备
        self.gsio.set_output_bit(0, False)  # 主电机
        self.gsio.set_output_bit(8, False)  # 电磁阀1
        self.gsio.set_output_bit(9, False)  # 电磁阀2
        
        # 点亮故障指示灯
        self.gsio.set_output_bit(5, True)   # 故障指示灯
    
    def start_sequence(self):
        """启动序列"""
        logger.info("执行启动序列")
        
        # 检查安全条件
        inputs = self.gsio.read_inputs()
        if inputs is None:
            return
            
        emergency_stop = bool(inputs & (1 << 2))
        safety_door = bool(inputs & (1 << 3))
        
        if emergency_stop or not safety_door:
            logger.warning("安全条件不满足，无法启动")
            return
        
        # 启动序列
        self.gsio.set_output_bit(6, False)  # 关闭待机指示灯
        self.gsio.set_output_bit(4, True)   # 点亮运行指示灯
        
        time.sleep(0.5)
        self.gsio.set_output_bit(1, True)   # 启动冷却泵
        
        time.sleep(1)
        self.gsio.set_output_bit(0, True)   # 启动主电机
        
        logger.info("启动序列完成")
    
    def stop_sequence(self):
        """停止序列"""
        logger.info("执行停止序列")
        
        # 停止序列
        self.gsio.set_output_bit(0, False)  # 停止主电机
        
        time.sleep(2)
        self.gsio.set_output_bit(1, False)  # 停止冷却泵
        self.gsio.set_output_bit(2, False)  # 关闭加热器
        
        self.gsio.set_output_bit(4, False)  # 关闭运行指示灯
        self.gsio.set_output_bit(6, True)   # 点亮待机指示灯
        
        logger.info("停止序列完成")
    
    def get_status_report(self) -> dict:
        """获取状态报告"""
        inputs = self.gsio.get_all_inputs_status()
        
        if inputs is None:
            return {"error": "无法读取设备状态"}
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "device_ip": self.gsio.ip_address,
            "inputs": {},
            "outputs": {},
            "alarms": self.alarms
        }
        
        # 输入状态
        for bit_str, config in self.io_config["inputs"].items():
            bit = int(bit_str)
            report["inputs"][config["name"]] = {
                "state": inputs.get(bit, False),
                "type": config.get("type", ""),
                "bit": bit
            }
        
        # 输出状态
        current_outputs = self.gsio.current_output_data
        for bit_str, config in self.io_config["outputs"].items():
            bit = int(bit_str)
            report["outputs"][config["name"]] = {
                "state": bool(current_outputs & (1 << bit)),
                "type": config.get("type", ""),
                "bit": bit
            }
        
        return report


def main():
    """主程序"""
    DEVICE_IP = "*************"  # 修改为实际设备IP
    
    controller = IndustrialIOController(DEVICE_IP)
    
    try:
        print("=== 工业IO控制系统 ===")
        print("1. 启动监控")
        print("2. 状态报告")
        print("3. 手动控制")
        print("0. 退出")
        
        while True:
            choice = input("\n请选择操作: ").strip()
            
            if choice == "1":
                if controller.start_monitoring():
                    print("监控已启动，按Ctrl+C停止...")
                    try:
                        while True:
                            time.sleep(1)
                    except KeyboardInterrupt:
                        print("\n停止监控...")
                        controller.stop_monitoring()
                        
            elif choice == "2":
                if not controller.gsio.driver:
                    controller.gsio.connect()
                
                report = controller.get_status_report()
                print(json.dumps(report, indent=2, ensure_ascii=False))
                
            elif choice == "3":
                # 手动控制模式
                if not controller.gsio.driver:
                    controller.gsio.connect()
                    
                controller.gsio.print_io_status()
                
                try:
                    bit = int(input("输入输出位号(0-15): "))
                    state = input("输入状态(on/off): ").lower() == "on"
                    
                    if controller.gsio.set_output_bit(bit, state):
                        print(f"输出{bit}设置为{'ON' if state else 'OFF'}成功")
                    else:
                        print("设置失败")
                except ValueError:
                    print("输入格式错误")
                    
            elif choice == "0":
                break
            else:
                print("无效选择")
                
    except KeyboardInterrupt:
        print("\n程序被中断")
    finally:
        controller.stop_monitoring()


if __name__ == "__main__":
    main()
