#!/usr/bin/env python3
"""
GSIO-B-EIC-1616P EtherNet/IP IO Controller
基于EDS文件的16输入16输出模块控制程序

设备信息:
- 厂商: GIENSO (VendCode: 32764)
- 产品: GSIO-B-EIC-1616P (ProdCode: 10006)
- 类型: 通用离散IO (16输入/16输出)
- 输入Assembly: 100 (2字节)
- 输出Assembly: 150 (2字节)
"""

import time
import struct
from pycomm3 import CIPDriver
from typing import Optional, Dict, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class GSIOController:
    """GSIO-B-EIC-1616P EtherNet/IP IO控制器"""
    
    def __init__(self, ip_address: str, slot: int = 0):
        """
        初始化GSIO控制器
        
        Args:
            ip_address: 设备IP地址
            slot: 设备槽位号 (默认0)
        """
        self.ip_address = ip_address
        self.slot = slot
        self.driver = None
        
        # 根据EDS文件的Assembly配置
        self.input_assembly = 100   # 输入Assembly实例
        self.output_assembly = 150  # 输出Assembly实例
        self.input_size = 2         # 输入数据大小(字节)
        self.output_size = 2        # 输出数据大小(字节)
        
        # 设备信息
        self.vendor_id = 32764      # GIENSO
        self.product_code = 10006   # GSIO-B-EIC-1616P
        
        # IO状态缓存
        self.last_input_data = 0
        self.current_output_data = 0
        
    def connect(self) -> bool:
        """
        连接到GSIO设备
        
        Returns:
            bool: 连接成功返回True
        """
        try:
            # 创建CIP驱动连接
            self.driver = CIPDriver(self.ip_address)
            
            # 尝试读取设备身份信息验证连接
            identity = self.driver.get_device_info()
            if identity:
                logger.info(f"成功连接到设备: {self.ip_address}")
                logger.info(f"设备信息: {identity}")
                return True
            else:
                logger.error("无法获取设备信息")
                return False
                
        except Exception as e:
            logger.error(f"连接失败: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.driver:
            self.driver.close()
            self.driver = None
            logger.info("已断开连接")
    
    def read_inputs(self) -> Optional[int]:
        """
        读取输入状态 (16位)
        
        Returns:
            int: 16位输入状态，None表示读取失败
        """
        if not self.driver:
            logger.error("设备未连接")
            return None
            
        try:
            # 读取输入Assembly
            result = self.driver.generic_message(
                service=0x0E,  # Get_Attribute_Single
                class_code=0x04,  # Assembly Object
                instance=self.input_assembly,
                attribute=0x03,  # Data attribute
                request_data=b''
            )
            
            if result and len(result.data) >= 2:
                # 解析2字节输入数据为16位整数
                input_value = struct.unpack('<H', result.data[:2])[0]
                self.last_input_data = input_value
                return input_value
            else:
                logger.error("读取输入数据失败")
                return None
                
        except Exception as e:
            logger.error(f"读取输入异常: {e}")
            return None
    
    def write_outputs(self, output_value: int) -> bool:
        """
        写入输出状态 (16位)
        
        Args:
            output_value: 16位输出值 (0-65535)
            
        Returns:
            bool: 写入成功返回True
        """
        if not self.driver:
            logger.error("设备未连接")
            return False
            
        if not (0 <= output_value <= 0xFFFF):
            logger.error("输出值超出范围 (0-65535)")
            return False
            
        try:
            # 将16位整数打包为2字节数据
            output_data = struct.pack('<H', output_value)
            
            # 写入输出Assembly
            result = self.driver.generic_message(
                service=0x10,  # Set_Attribute_Single
                class_code=0x04,  # Assembly Object
                instance=self.output_assembly,
                attribute=0x03,  # Data attribute
                request_data=output_data
            )
            
            if result and result.service == 0x90:  # Success response
                self.current_output_data = output_value
                return True
            else:
                logger.error("写入输出数据失败")
                return False
                
        except Exception as e:
            logger.error(f"写入输出异常: {e}")
            return False
    
    def get_input_bit(self, bit_index: int) -> Optional[bool]:
        """
        获取指定输入位状态
        
        Args:
            bit_index: 位索引 (0-15)
            
        Returns:
            bool: 位状态，None表示读取失败
        """
        if not (0 <= bit_index <= 15):
            logger.error("位索引超出范围 (0-15)")
            return None
            
        input_data = self.read_inputs()
        if input_data is not None:
            return bool(input_data & (1 << bit_index))
        return None
    
    def set_output_bit(self, bit_index: int, state: bool) -> bool:
        """
        设置指定输出位状态
        
        Args:
            bit_index: 位索引 (0-15)
            state: 位状态 (True/False)
            
        Returns:
            bool: 设置成功返回True
        """
        if not (0 <= bit_index <= 15):
            logger.error("位索引超出范围 (0-15)")
            return False
            
        if state:
            new_output = self.current_output_data | (1 << bit_index)
        else:
            new_output = self.current_output_data & ~(1 << bit_index)
            
        return self.write_outputs(new_output)
    
    def get_all_inputs_status(self) -> Optional[Dict[int, bool]]:
        """
        获取所有输入位状态
        
        Returns:
            dict: {位索引: 状态} 字典，None表示读取失败
        """
        input_data = self.read_inputs()
        if input_data is not None:
            return {i: bool(input_data & (1 << i)) for i in range(16)}
        return None
    
    def print_io_status(self):
        """打印当前IO状态"""
        inputs = self.get_all_inputs_status()
        if inputs is not None:
            print(f"\n=== GSIO-B-EIC-1616P IO状态 ({self.ip_address}) ===")
            print(f"输入状态 (0x{self.last_input_data:04X}):")
            for i in range(16):
                status = "ON " if inputs[i] else "OFF"
                print(f"  输入{i:2d}: {status}", end="  ")
                if (i + 1) % 4 == 0:
                    print()
            
            print(f"\n输出状态 (0x{self.current_output_data:04X}):")
            for i in range(16):
                status = "ON " if (self.current_output_data & (1 << i)) else "OFF"
                print(f"  输出{i:2d}: {status}", end="  ")
                if (i + 1) % 4 == 0:
                    print()
            print()


def main():
    """主程序示例"""
    # 设备IP地址 - 请根据实际情况修改
    DEVICE_IP = "*************"
    
    # 创建控制器实例
    gsio = GSIOController(DEVICE_IP)
    
    try:
        # 连接设备
        if not gsio.connect():
            print("连接设备失败")
            return
        
        print("=== GSIO-B-EIC-1616P 控制程序 ===")
        print("1. 读取输入状态")
        print("2. 控制输出")
        print("3. 监控模式")
        print("4. 测试模式")
        print("0. 退出")
        
        while True:
            choice = input("\n请选择操作: ").strip()
            
            if choice == "1":
                # 读取并显示输入状态
                gsio.print_io_status()
                
            elif choice == "2":
                # 控制输出
                try:
                    bit_index = int(input("输入输出位索引 (0-15): "))
                    state = input("输入状态 (on/off): ").lower() == "on"
                    
                    if gsio.set_output_bit(bit_index, state):
                        print(f"输出{bit_index}设置为{'ON' if state else 'OFF'}成功")
                    else:
                        print("设置输出失败")
                except ValueError:
                    print("输入格式错误")
                    
            elif choice == "3":
                # 监控模式
                print("进入监控模式，按Ctrl+C退出...")
                try:
                    while True:
                        gsio.print_io_status()
                        time.sleep(1)
                except KeyboardInterrupt:
                    print("\n退出监控模式")
                    
            elif choice == "4":
                # 测试模式 - 循环点亮输出
                print("进入测试模式，循环点亮输出，按Ctrl+C退出...")
                try:
                    while True:
                        for i in range(16):
                            gsio.write_outputs(1 << i)
                            print(f"点亮输出{i}")
                            time.sleep(0.5)
                        gsio.write_outputs(0)  # 全部关闭
                        time.sleep(1)
                except KeyboardInterrupt:
                    gsio.write_outputs(0)  # 关闭所有输出
                    print("\n退出测试模式")
                    
            elif choice == "0":
                break
            else:
                print("无效选择")
                
    except KeyboardInterrupt:
        print("\n程序被中断")
    finally:
        # 断开连接
        gsio.disconnect()


if __name__ == "__main__":
    main()
