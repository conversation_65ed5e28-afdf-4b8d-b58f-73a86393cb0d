# GSIO-B-EIC-1616P EtherNet/IP 控制程序

基于pycomm3库的GIENSO GSIO-B-EIC-1616P (16输入16输出) EtherNet/IP IO模块控制程序。

## 设备信息

根据EDS文件分析：
- **厂商**: GIENSO (VendCode: 32764)
- **产品**: GSIO-B-EIC-1616P (ProdCode: 10006)
- **类型**: 通用离散IO模块
- **输入**: 16路数字输入
- **输出**: 16路数字输出
- **通信**: EtherNet/IP协议
- **输入Assembly**: 100 (2字节数据)
- **输出Assembly**: 150 (2字节数据)
- **默认RPI**: 20ms (可配置范围: 0.5ms - 50ms)

## 安装依赖

```bash
pip install -r requirements.txt
```

或直接安装：

```bash
pip install pycomm3
```

## 使用方法

### 1. 连接测试

首先运行连接测试，确保设备通信正常：

```bash
python test_connection.py *************
```

或者运行后输入IP地址：

```bash
python test_connection.py
```

### 2. 基本配置

修改程序中的设备IP地址：

```python
DEVICE_IP = "*************"  # 修改为实际设备IP
```

### 3. 运行示例程序

#### 简单示例
```bash
python simple_gsio_example.py
```

#### 完整控制程序
```bash
python gsio_eip_controller.py
```

#### 工业应用示例
```bash
python industrial_example.py
```

### 4. 程序功能

#### test_connection.py
- 设备连接测试
- Assembly访问验证
- 基本IO功能测试

#### simple_gsio_example.py
- 基本IO读写操作
- 输入状态监控
- 输出模式测试

#### gsio_eip_controller.py
- 完整的设备控制类
- 交互式操作界面
- 实时监控功能
- 测试模式

#### industrial_example.py
- 工业级应用示例
- 安全联锁功能
- 配置文件管理
- 数据记录和报警

## API说明

### GSIOController类

```python
from gsio_eip_controller import GSIOController

# 创建控制器
gsio = GSIOController("*************")

# 连接设备
if gsio.connect():
    # 读取所有输入
    inputs = gsio.read_inputs()  # 返回16位整数
    
    # 读取单个输入位
    bit_state = gsio.get_input_bit(0)  # 读取输入0
    
    # 设置单个输出位
    gsio.set_output_bit(0, True)   # 设置输出0为ON
    gsio.set_output_bit(1, False)  # 设置输出1为OFF
    
    # 设置所有输出
    gsio.write_outputs(0x00FF)  # 设置输出0-7为ON，8-15为OFF
    
    # 获取所有输入状态
    all_inputs = gsio.get_all_inputs_status()  # 返回字典
    
    # 显示IO状态
    gsio.print_io_status()
    
    # 断开连接
    gsio.disconnect()
```

### 直接使用pycomm3

```python
from pycomm3 import CIPDriver
import struct

with CIPDriver("***************") as plc:
    # 读取输入Assembly (100)
    result = plc.generic_message(
        service=0x0E,        # Get_Attribute_Single
        class_code=0x04,     # Assembly Object
        instance=100,        # Input Assembly
        attribute=0x03,      # Data attribute
        request_data=b''
    )
    
    if result:
        input_value = struct.unpack('<H', result.data[:2])[0]
        print(f"输入状态: 0x{input_value:04X}")
    
    # 写入输出Assembly (150)
    output_data = struct.pack('<H', 0x00FF)  # 设置输出0-7
    result = plc.generic_message(
        service=0x10,        # Set_Attribute_Single
        class_code=0x04,     # Assembly Object
        instance=150,        # Output Assembly
        attribute=0x03,      # Data attribute
        request_data=output_data
    )
```

## 数据格式

### 输入数据 (2字节)
- 位0-15: 对应输入通道0-15
- 数据类型: 16位无符号整数 (Little Endian)
- 1 = 输入有效, 0 = 输入无效

### 输出数据 (2字节)
- 位0-15: 对应输出通道0-15  
- 数据类型: 16位无符号整数 (Little Endian)
- 1 = 输出ON, 0 = 输出OFF

## 网络配置

确保：
1. PC与GSIO设备在同一网段
2. 设备IP地址配置正确
3. 防火墙允许EtherNet/IP通信 (端口44818)

## 故障排除

### 连接失败
1. 检查网络连接和IP地址
2. 确认设备电源和网络指示灯状态
3. 使用ping命令测试网络连通性

### 读写失败
1. 检查Assembly实例号是否正确
2. 确认设备支持的服务代码
3. 查看设备状态指示灯

### 数据异常
1. 确认数据格式和字节序
2. 检查Assembly数据长度
3. 验证位操作的索引范围

## 注意事项

1. **安全**: 在工业环境中使用时，确保遵循安全操作规程
2. **实时性**: EtherNet/IP的实时性取决于网络配置和RPI设置
3. **错误处理**: 生产环境中应添加完善的错误处理和重连机制
4. **并发**: 避免多个程序同时访问同一设备

## 快速开始

运行快速启动脚本，通过菜单选择功能：

```bash
python quick_start.py
```

## 重要更新

⚠️ **已根据 pycomm3 官方文档进行重要修正**

我们已经根据 [pycomm3 官方文档](https://docs.pycomm3.dev/en/latest/) 对所有程序进行了修正，主要改进包括：

- 使用正确的 `Services` 和 `ClassCode` 枚举常量
- 修正 `generic_message` 方法的参数格式
- 改进数据类型处理和错误处理
- 使用官方推荐的API调用方式

详细修正说明请查看 `PYCOMM3_CORRECTIONS.md` 文件。

## 文件说明

| 文件名 | 说明 |
|--------|------|
| `quick_start.py` | 快速启动脚本，提供菜单界面 |
| `test_connection.py` | 设备连接测试脚本 |
| `simple_gsio_example.py` | 简单IO控制示例 |
| `gsio_eip_controller.py` | 完整的设备控制类 |
| `industrial_example.py` | 工业级应用示例 |
| `io_config.json` | IO配置文件 |
| `requirements.txt` | Python依赖包列表 |
| `GSIO-B-EIC-1616P_V1.1.eds` | 设备EDS文件 |

## 许可证

本程序仅供学习和参考使用。在生产环境中使用前，请进行充分测试。
