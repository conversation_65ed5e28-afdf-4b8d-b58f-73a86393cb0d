# pycomm3 用法修正说明

根据 pycomm3 官方文档 (https://docs.pycomm3.dev/en/latest/)，我们对之前创建的程序进行了重要修正。

## 主要修正内容

### 1. 导入修正
**之前的错误用法:**
```python
from pycomm3 import CIPDriver
```

**修正后的正确用法:**
```python
from pycomm3 import CIPDriver, Services, ClassCode, UINT
```

### 2. generic_message 方法调用修正

**之前的错误用法:**
```python
result = plc.generic_message(
    service=0x0E,           # 使用原始数值
    class_code=0x04,        # 使用原始数值
    instance=INPUT_ASSEMBLY,
    attribute=0x03,         # 使用原始数值
    request_data=b''        # 错误的参数
)
```

**修正后的正确用法:**
```python
result = plc.generic_message(
    service=Services.get_attribute_single,  # 使用预定义常量
    class_code=ClassCode.assembly,          # 使用预定义常量
    instance=INPUT_ASSEMBLY,
    attribute=3,                            # 使用整数
    data_type=UINT,                         # 指定数据类型
    connected=False,                        # 明确连接模式
    unconnected_send=True,                  # 使用无连接发送
    route_path=False,                       # 路径设置
    name='read_inputs'                      # 操作名称（用于日志）
)
```

### 3. 响应数据处理修正

**之前的错误用法:**
```python
if result and len(result.data) >= 2:
    input_value = struct.unpack('<H', result.data[:2])[0]
```

**修正后的正确用法:**
```python
if result:
    input_value = result.value  # 直接获取解析后的值
```

### 4. 写入数据格式修正

**之前的错误用法:**
```python
output_data = struct.pack('<H', output_value)
result = plc.generic_message(
    service=0x10,
    class_code=0x04,
    instance=OUTPUT_ASSEMBLY,
    attribute=0x03,
    request_data=output_data
)
```

**修正后的正确用法:**
```python
result = plc.generic_message(
    service=Services.set_attribute_single,
    class_code=ClassCode.assembly,
    instance=OUTPUT_ASSEMBLY,
    attribute=3,
    request_data=UINT.encode(output_value),  # 使用数据类型编码
    connected=False,
    unconnected_send=True,
    route_path=False,
    name='write_outputs'
)
```

### 5. 设备信息获取修正

**之前的错误用法:**
```python
device_info = plc.get_device_info()  # 不存在的方法
```

**修正后的正确用法:**
```python
device_info = plc.list_identity()  # 正确的方法
```

## 关键改进点

1. **使用预定义常量**: 使用 `Services` 和 `ClassCode` 枚举而不是原始数值
2. **指定数据类型**: 使用 `data_type=UINT` 让 pycomm3 自动处理数据解析
3. **明确连接参数**: 设置 `connected=False, unconnected_send=True` 用于无连接通信
4. **简化数据处理**: 直接使用 `result.value` 获取解析后的数据
5. **改进错误处理**: 使用 `result.error` 获取详细错误信息
6. **添加操作名称**: 使用 `name` 参数便于调试和日志记录

## 修正的文件

- `simple_gsio_example.py` - 简单IO控制示例
- `test_connection.py` - 连接测试脚本  
- `gsio_eip_controller.py` - 完整控制器类（部分修正）
- `industrial_example.py` - 工业应用示例（需要类似修正）

## 注意事项

1. 这些修正确保了与 pycomm3 1.2.14 版本的兼容性
2. 使用官方推荐的 API 调用方式
3. 提供了更好的错误处理和调试信息
4. 代码更加健壮和可维护

## 测试建议

在实际硬件上测试前，建议：
1. 确认设备IP地址正确
2. 检查网络连接
3. 验证EDS文件中的Assembly实例号
4. 使用连接测试脚本验证基本通信

这些修正使程序更符合pycomm3的最佳实践，提高了可靠性和可维护性。

## 🔥 重要更新 - 连接模式修正

**2025年1月31日更新**: 通过诊断工具发现，您的GSIO设备需要使用**连接模式**而不是标准的无连接模式。

### 关键发现
- 设备支持TCP连接（端口44818）
- 设备不响应UDP广播发现
- 设备不支持标准的无连接模式通信
- **设备需要连接模式**: `connected=True, unconnected_send=False`

### 最终正确配置
```python
result = plc.generic_message(
    service=Services.get_attribute_single,
    class_code=ClassCode.assembly,
    instance=INPUT_ASSEMBLY,
    attribute=3,
    data_type=UINT,
    connected=True,          # ✓ 必须使用连接模式
    unconnected_send=False,  # ✓ 关闭无连接发送
    route_path=False,
    name='read_inputs'
)
```

### 诊断结果
- ✅ TCP连接成功
- ❌ UDP发现失败（正常，某些设备不支持）
- ❌ 设备身份识别失败（正常，某些设备不支持）
- ✅ 基本连接成功
- ✅ **连接模式成功，数据: 0x09F2**
- ❌ 标准无连接模式失败

### 已更新的文件
- ✅ `simple_gsio_example.py` - 所有连接参数已更新
- ✅ `test_connection.py` - 所有连接参数已更新
- ✅ `gsio_eip_controller.py` - 主要方法已更新
- ✅ `ethernet_ip_diagnosis.py` - 新增诊断工具
- ✅ `gsio_fixed_connection.py` - 新增修复版控制程序

现在您的程序应该能够正常连接和控制GSIO设备了！
