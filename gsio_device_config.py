#!/usr/bin/env python3
"""
GSIO设备配置检查和修复工具
基于产品手册的完整配置方案
"""

import requests
import socket
import subprocess
import time
from typing import Optional, Dict

class GSIODeviceConfig:
    """GSIO设备配置管理器"""
    
    def __init__(self):
        self.config_ip = "*************"
        self.config_port = 2250
        self.default_comm_ip = "***************"
        self.username = "admin"
        self.password = "admin"
    
    def check_network_connectivity(self) -> Dict[str, bool]:
        """检查网络连通性"""
        print("检查网络连通性...")
        results = {}
        
        # 检查配置IP
        print(f"1. 检查配置IP {self.config_ip}...")
        results['config_ping'] = self._ping(self.config_ip)
        results['config_web'] = self._check_web_access()
        
        # 检查通讯IP
        print(f"2. 检查通讯IP {self.default_comm_ip}...")
        results['comm_ping'] = self._ping(self.default_comm_ip)
        results['comm_eip'] = self._check_eip_port(self.default_comm_ip)
        
        return results
    
    def _ping(self, ip: str, timeout: int = 3) -> bool:
        """Ping测试"""
        try:
            result = subprocess.run(
                ['ping', '-c', '1', '-W', str(timeout * 1000), ip],
                capture_output=True,
                text=True
            )
            success = result.returncode == 0
            print(f"   Ping {ip}: {'✓' if success else '✗'}")
            return success
        except:
            print(f"   Ping {ip}: ✗")
            return False
    
    def _check_web_access(self) -> bool:
        """检查Web访问"""
        try:
            url = f"http://{self.config_ip}:{self.config_port}"
            response = requests.get(url, timeout=5)
            success = response.status_code == 200
            print(f"   Web访问 {url}: {'✓' if success else '✗'}")
            return success
        except Exception as e:
            print(f"   Web访问: ✗ ({e})")
            return False
    
    def _check_eip_port(self, ip: str, port: int = 44818) -> bool:
        """检查EtherNet/IP端口"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(3)
            result = sock.connect_ex((ip, port))
            sock.close()
            success = result == 0
            print(f"   EIP端口 {ip}:{port}: {'✓' if success else '✗'}")
            return success
        except:
            print(f"   EIP端口 {ip}:{port}: ✗")
            return False
    
    def get_device_config(self) -> Optional[Dict]:
        """获取设备配置"""
        print(f"\n获取设备配置...")
        
        try:
            # 尝试登录并获取配置
            session = requests.Session()
            
            # 登录
            login_url = f"http://{self.config_ip}:{self.config_port}/login"
            login_data = {
                'username': self.username,
                'password': self.password
            }
            
            # 有些设备可能不需要登录，直接访问配置页面
            config_url = f"http://{self.config_ip}:{self.config_port}"
            
            response = session.get(config_url, timeout=10)
            
            if response.status_code == 200:
                print("✓ 成功访问设备配置页面")
                
                # 解析配置信息（这里简化处理）
                config = {
                    'accessible': True,
                    'response_length': len(response.text),
                    'contains_ip_config': 'IP' in response.text.upper(),
                    'contains_gateway': 'GATEWAY' in response.text.upper() or '网关' in response.text
                }
                
                print(f"   页面长度: {config['response_length']} 字符")
                print(f"   包含IP配置: {'✓' if config['contains_ip_config'] else '✗'}")
                print(f"   包含网关配置: {'✓' if config['contains_gateway'] else '✗'}")
                
                return config
            else:
                print(f"✗ 访问失败: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            print(f"✗ 获取配置失败: {e}")
            return None
    
    def suggest_network_fix(self):
        """建议网络修复方案"""
        print("\n" + "=" * 60)
        print("网络配置修复建议")
        print("=" * 60)
        
        print("\n📋 根据产品手册的配置步骤:")
        
        print("\n1️⃣ 设备端配置 (通过Web界面 *************:2250):")
        print("   - 用户名: admin")
        print("   - 密码: admin")
        print("   - IP地址: *************** (与您的网络匹配)")
        print("   - 子网掩码: *************")
        print("   - 网关地址: 0.0.0.0 (留空或设为0.0.0.0)")
        print("   - 通讯超时时间: 建议选择 1s")
        print("   - 主机STOP清除: 建议选择 是")
        
        print("\n2️⃣ PC端网络配置:")
        print("   - 临时设置PC IP为 ************* (用于访问配置页面)")
        print("   - 子网掩码: *************")
        print("   - 网关: 留空")
        
        print("\n3️⃣ 配置完成后:")
        print("   - 设备断电重启")
        print("   - PC IP改为 ***************")
        print("   - 测试通讯")
        
        print("\n4️⃣ macOS网络配置命令:")
        print("   # 临时配置用于设备配置")
        print("   sudo ifconfig en0 ************* netmask *************")
        print("   ")
        print("   # 配置完成后改为通讯IP")
        print("   sudo ifconfig en0 *************** netmask *************")
        print("   sudo route delete default")
        
        print("\n5️⃣ 验证步骤:")
        print("   - ping ***************")
        print("   - 运行 gsio_manual_based_solution.py")
    
    def test_pycomm3_connection(self, ip: str) -> bool:
        """测试pycomm3连接"""
        print(f"\n测试pycomm3连接到 {ip}...")
        
        try:
            from pycomm3 import CIPDriver, Services, ClassCode, UINT
            
            driver = CIPDriver(ip)
            driver.open()
            
            # 尝试读取输入
            result = driver.generic_message(
                service=Services.get_attribute_single,
                class_code=ClassCode.assembly,
                instance=100,  # 输入Assembly
                attribute=3,
                data_type=UINT,
                connected=True,
                unconnected_send=False,
                route_path=False,
                name='test_read'
            )
            
            driver.close()
            
            if result:
                print(f"✓ pycomm3连接成功，读取到数据: 0x{result.value:04X}")
                return True
            else:
                print(f"✗ 连接成功但读取失败: {result.error if result else '无响应'}")
                return False
                
        except ImportError:
            print("✗ pycomm3未安装，请运行: pip install pycomm3")
            return False
        except Exception as e:
            print(f"✗ pycomm3连接失败: {e}")
            return False
    
    def run_diagnosis(self):
        """运行完整诊断"""
        print("=" * 60)
        print("    GSIO设备配置诊断工具")
        print("    基于产品手册 Ver 1.0")
        print("=" * 60)
        
        # 1. 网络连通性检查
        connectivity = self.check_network_connectivity()
        
        # 2. 设备配置检查
        config = self.get_device_config()
        
        # 3. pycomm3连接测试
        if connectivity.get('comm_ping', False):
            self.test_pycomm3_connection(self.default_comm_ip)
        
        # 4. 问题分析和建议
        print("\n" + "=" * 60)
        print("诊断结果分析")
        print("=" * 60)
        
        if not connectivity.get('config_ping', False):
            print("❌ 无法ping通配置IP (*************)")
            print("   原因: PC与设备不在同一网段")
            print("   解决: 临时设置PC IP为 192.168.1.x")
        
        if not connectivity.get('config_web', False):
            print("❌ 无法访问设备配置页面")
            print("   原因: 网络不通或设备未启动")
            print("   解决: 检查网络连接和设备电源")
        
        if not connectivity.get('comm_ping', False):
            print("❌ 无法ping通通讯IP (***************)")
            print("   原因: 设备通讯IP未配置或网络不匹配")
            print("   解决: 通过配置页面修改设备IP")
        
        if connectivity.get('config_web', False) and not connectivity.get('comm_ping', False):
            print("⚠️  可以访问配置页面但无法通讯")
            print("   原因: 设备通讯IP配置错误")
            print("   解决: 登录配置页面修改IP为 ***************")
        
        # 5. 显示修复建议
        self.suggest_network_fix()

def main():
    config_tool = GSIODeviceConfig()
    config_tool.run_diagnosis()
    
    print("\n" + "=" * 60)
    print("下一步操作:")
    print("1. 按照上述建议配置网络")
    print("2. 重新运行此工具验证配置")
    print("3. 使用 gsio_manual_based_solution.py 测试I/O功能")
    print("=" * 60)

if __name__ == "__main__":
    main()
