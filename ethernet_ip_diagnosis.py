#!/usr/bin/env python3
"""
EtherNet/IP 连接诊断和修复脚本
基于日文帖子的经验和pycomm3最佳实践
"""

import socket
import time
import struct
from pycomm3 import CIPDriver, Services, ClassCode, UINT

def test_basic_connectivity(ip_address: str, port: int = 44818) -> bool:
    """测试基本TCP连接"""
    print(f"测试TCP连接到 {ip_address}:{port}...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        result = sock.connect_ex((ip_address, port))
        sock.close()
        
        if result == 0:
            print("✓ TCP连接成功")
            return True
        else:
            print(f"✗ TCP连接失败，错误代码: {result}")
            return False
    except Exception as e:
        print(f"✗ TCP连接异常: {e}")
        return False

def test_udp_discovery():
    """测试UDP广播发现设备"""
    print("\n测试UDP广播发现设备...")
    try:
        devices = CIPDriver.discover()
        if devices:
            print(f"✓ 发现 {len(devices)} 个设备:")
            for i, device in enumerate(devices):
                print(f"  设备 {i+1}:")
                print(f"    IP: {device.get('ip_address', 'Unknown')}")
                print(f"    厂商: {device.get('vendor', 'Unknown')}")
                print(f"    产品: {device.get('product_name', 'Unknown')}")
                print(f"    产品代码: {device.get('product_code', 'Unknown')}")
        else:
            print("✗ 未发现任何设备")
        return devices
    except Exception as e:
        print(f"✗ 设备发现失败: {e}")
        return []

def test_list_identity(ip_address: str):
    """测试单个设备身份识别"""
    print(f"\n测试设备身份识别: {ip_address}")
    try:
        device_info = CIPDriver.list_identity(ip_address)
        if device_info:
            print("✓ 设备身份识别成功:")
            print(f"    IP: {device_info.get('ip_address', 'Unknown')}")
            print(f"    厂商: {device_info.get('vendor', 'Unknown')}")
            print(f"    产品: {device_info.get('product_name', 'Unknown')}")
            print(f"    产品代码: {device_info.get('product_code', 'Unknown')}")
            print(f"    厂商ID: {device_info.get('vendor_id', 'Unknown')}")
            return device_info
        else:
            print("✗ 设备身份识别失败")
            return None
    except Exception as e:
        print(f"✗ 设备身份识别异常: {e}")
        return None

def test_connection_methods(ip_address: str):
    """测试不同的连接方法"""
    print(f"\n测试不同连接方法: {ip_address}")
    
    # 方法1: 基本连接
    print("方法1: 基本连接")
    try:
        with CIPDriver(ip_address) as driver:
            print("✓ 基本连接成功")
            return True
    except Exception as e:
        print(f"✗ 基本连接失败: {e}")
    
    # 方法2: 显式打开连接
    print("方法2: 显式打开连接")
    try:
        driver = CIPDriver(ip_address)
        driver.open()
        print("✓ 显式连接成功")
        driver.close()
        return True
    except Exception as e:
        print(f"✗ 显式连接失败: {e}")
    
    # 方法3: 设置超时
    print("方法3: 设置超时连接")
    try:
        driver = CIPDriver(ip_address)
        driver.socket_timeout = 10  # 10秒超时
        driver.open()
        print("✓ 超时连接成功")
        driver.close()
        return True
    except Exception as e:
        print(f"✗ 超时连接失败: {e}")
    
    return False

def test_assembly_access_methods(ip_address: str):
    """测试不同的Assembly访问方法"""
    print(f"\n测试Assembly访问方法: {ip_address}")
    
    # 根据日文帖子的经验，尝试不同的参数组合
    test_configs = [
        {
            "name": "标准无连接模式",
            "connected": False,
            "unconnected_send": True,
            "route_path": False
        },
        {
            "name": "连接模式",
            "connected": True,
            "unconnected_send": False,
            "route_path": False
        },
        {
            "name": "路径模式",
            "connected": False,
            "unconnected_send": True,
            "route_path": True
        },
        {
            "name": "简化模式",
            "connected": False,
            "unconnected_send": False,
            "route_path": False
        }
    ]
    
    for config in test_configs:
        print(f"\n测试配置: {config['name']}")
        try:
            with CIPDriver(ip_address) as driver:
                # 尝试读取Assembly 100 (输入)
                result = driver.generic_message(
                    service=Services.get_attribute_single,
                    class_code=ClassCode.assembly,
                    instance=100,
                    attribute=3,
                    data_type=UINT,
                    connected=config["connected"],
                    unconnected_send=config["unconnected_send"],
                    route_path=config["route_path"],
                    name=f'test_assembly_{config["name"]}'
                )
                
                if result:
                    print(f"✓ {config['name']} 成功，数据: 0x{result.value:04X}")
                    return config
                else:
                    print(f"✗ {config['name']} 失败: {result.error if result else '无响应'}")
                    
        except Exception as e:
            print(f"✗ {config['name']} 异常: {e}")
    
    return None

def test_raw_cip_message(ip_address: str):
    """测试原始CIP消息（参考日文帖子）"""
    print(f"\n测试原始CIP消息: {ip_address}")
    
    try:
        with CIPDriver(ip_address) as driver:
            # 参考日文帖子，使用原始服务代码
            result = driver.generic_message(
                service=0x01,  # Get_Attributes_All
                class_code=0x04,  # Assembly Object
                instance=100,
                connected=False
            )
            
            if result:
                print(f"✓ 原始CIP消息成功")
                print(f"    响应数据长度: {len(result) if hasattr(result, '__len__') else 'N/A'}")
                return True
            else:
                print("✗ 原始CIP消息失败")
                
    except Exception as e:
        print(f"✗ 原始CIP消息异常: {e}")
    
    return False

def main():
    print("=" * 60)
    print("    EtherNet/IP 连接诊断工具")
    print("    基于pycomm3和日文帖子经验")
    print("=" * 60)
    
    # 获取设备IP
    ip_address = input("请输入设备IP地址: ").strip()
    if not ip_address:
        print("未输入IP地址，退出")
        return
    
    print(f"\n开始诊断设备: {ip_address}")
    
    # 1. 基本连接测试
    tcp_ok = test_basic_connectivity(ip_address)
    
    # 2. UDP发现测试
    discovered_devices = test_udp_discovery()
    
    # 3. 设备身份识别
    device_info = test_list_identity(ip_address)
    
    # 4. 连接方法测试
    connection_ok = test_connection_methods(ip_address)
    
    if connection_ok:
        # 5. Assembly访问测试
        working_config = test_assembly_access_methods(ip_address)
        
        # 6. 原始CIP消息测试
        raw_cip_ok = test_raw_cip_message(ip_address)
        
        # 生成建议
        print("\n" + "=" * 60)
        print("诊断结果和建议:")
        print("=" * 60)
        
        if working_config:
            print(f"✓ 推荐使用配置: {working_config['name']}")
            print("  参数设置:")
            for key, value in working_config.items():
                if key != 'name':
                    print(f"    {key}={value}")
        else:
            print("⚠ 未找到有效的Assembly访问配置")
            print("  建议检查:")
            print("  1. EDS文件中的Assembly实例号是否正确")
            print("  2. 设备是否支持标准Assembly对象")
            print("  3. 设备是否需要特殊的连接参数")
    
    else:
        print("\n" + "=" * 60)
        print("连接失败诊断:")
        print("=" * 60)
        print("可能的原因:")
        print("1. 设备不支持EtherNet/IP协议")
        print("2. 设备防火墙阻止了44818端口")
        print("3. 设备需要特殊的初始化序列")
        print("4. 网络配置问题（VLAN、子网等）")
        print("5. 设备处于错误状态，需要重置")

if __name__ == "__main__":
    main()
