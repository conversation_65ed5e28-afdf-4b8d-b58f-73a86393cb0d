#!/usr/bin/env python3
"""
GSIO-B-EIC-1616P 修复版连接程序
基于日文帖子经验和pycomm3最佳实践
"""

import time
import struct
from pycomm3 import CIPDriver, Services, ClassCode, UINT
from typing import Optional

class GSIOFixedController:
    """修复版GSIO控制器"""
    
    def __init__(self, ip_address: str):
        self.ip_address = ip_address
        self.driver = None
        self.connection_config = None
        
        # 根据日文帖子经验，尝试多种连接配置
        self.connection_configs = [
            {
                "name": "标准无连接模式",
                "connected": False,
                "unconnected_send": True,
                "route_path": False
            },
            {
                "name": "简化模式",
                "connected": False,
                "unconnected_send": False,
                "route_path": False
            },
            {
                "name": "显式打开模式",
                "use_explicit_open": True,
                "connected": False,
                "unconnected_send": True,
                "route_path": False
            }
        ]
    
    def connect(self) -> bool:
        """尝试连接设备"""
        print(f"尝试连接到设备: {self.ip_address}")
        
        # 首先尝试设备发现
        try:
            device_info = CIPDriver.list_identity(self.ip_address)
            if device_info:
                print(f"✓ 设备发现成功: {device_info.get('product_name', 'Unknown')}")
            else:
                print("⚠ 设备发现失败，但继续尝试连接...")
        except Exception as e:
            print(f"⚠ 设备发现异常: {e}，但继续尝试连接...")
        
        # 尝试不同的连接配置
        for config in self.connection_configs:
            print(f"尝试配置: {config['name']}")
            
            try:
                if config.get('use_explicit_open'):
                    # 显式打开连接（参考日文帖子）
                    self.driver = CIPDriver(self.ip_address)
                    self.driver.socket_timeout = 10
                    self.driver.open()
                else:
                    # 使用with语句
                    self.driver = CIPDriver(self.ip_address)
                
                # 测试连接是否有效
                if self._test_connection(config):
                    self.connection_config = config
                    print(f"✓ 连接成功，使用配置: {config['name']}")
                    return True
                else:
                    if self.driver:
                        try:
                            self.driver.close()
                        except:
                            pass
                        self.driver = None
                        
            except Exception as e:
                print(f"✗ 配置 {config['name']} 失败: {e}")
                if self.driver:
                    try:
                        self.driver.close()
                    except:
                        pass
                    self.driver = None
        
        print("✗ 所有连接配置都失败")
        return False
    
    def _test_connection(self, config: dict) -> bool:
        """测试连接配置是否有效"""
        try:
            # 尝试读取输入Assembly
            result = self.driver.generic_message(
                service=Services.get_attribute_single,
                class_code=ClassCode.assembly,
                instance=100,  # 输入Assembly
                attribute=3,
                data_type=UINT,
                connected=config.get("connected", False),
                unconnected_send=config.get("unconnected_send", True),
                route_path=config.get("route_path", False),
                name='test_connection'
            )
            
            return result is not None
            
        except Exception:
            return False
    
    def read_inputs(self) -> Optional[int]:
        """读取输入状态"""
        if not self.driver or not self.connection_config:
            print("设备未连接")
            return None
        
        try:
            result = self.driver.generic_message(
                service=Services.get_attribute_single,
                class_code=ClassCode.assembly,
                instance=100,
                attribute=3,
                data_type=UINT,
                connected=self.connection_config.get("connected", False),
                unconnected_send=self.connection_config.get("unconnected_send", True),
                route_path=self.connection_config.get("route_path", False),
                name='read_inputs'
            )
            
            if result:
                return result.value
            else:
                print(f"读取输入失败: {result.error if result else '无响应'}")
                return None
                
        except Exception as e:
            print(f"读取输入异常: {e}")
            return None
    
    def write_outputs(self, output_value: int) -> bool:
        """写入输出状态"""
        if not self.driver or not self.connection_config:
            print("设备未连接")
            return False
        
        if not (0 <= output_value <= 0xFFFF):
            print("输出值超出范围 (0-65535)")
            return False
        
        try:
            result = self.driver.generic_message(
                service=Services.set_attribute_single,
                class_code=ClassCode.assembly,
                instance=150,
                attribute=3,
                request_data=UINT.encode(output_value),
                connected=self.connection_config.get("connected", False),
                unconnected_send=self.connection_config.get("unconnected_send", True),
                route_path=self.connection_config.get("route_path", False),
                name='write_outputs'
            )
            
            if result:
                return True
            else:
                print(f"写入输出失败: {result.error if result else '无响应'}")
                return False
                
        except Exception as e:
            print(f"写入输出异常: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.driver:
            try:
                self.driver.close()
            except:
                pass
            self.driver = None
            self.connection_config = None
            print("已断开连接")

def main():
    print("=" * 50)
    print("GSIO-B-EIC-1616P 修复版控制程序")
    print("基于日文帖子经验")
    print("=" * 50)
    
    # 获取设备IP
    ip_address = input("请输入设备IP地址: ").strip()
    if not ip_address:
        print("未输入IP地址，退出")
        return
    
    # 创建控制器
    controller = GSIOFixedController(ip_address)
    
    # 尝试连接
    if not controller.connect():
        print("连接失败，请运行 ethernet_ip_diagnosis.py 进行详细诊断")
        return
    
    try:
        while True:
            print("\n" + "=" * 40)
            print("选择操作:")
            print("1. 读取输入状态")
            print("2. 写入输出状态")
            print("3. 输出测试模式")
            print("4. 监控输入变化")
            print("0. 退出")
            print("=" * 40)
            
            choice = input("请选择 (0-4): ").strip()
            
            if choice == "1":
                # 读取输入
                inputs = controller.read_inputs()
                if inputs is not None:
                    print(f"输入状态: 0x{inputs:04X} ({inputs:016b})")
                    for i in range(16):
                        state = "ON " if (inputs & (1 << i)) else "OFF"
                        print(f"  输入{i:2d}: {state}")
                
            elif choice == "2":
                # 写入输出
                try:
                    output_str = input("请输入输出值 (十六进制，如 0x0001): ").strip()
                    if output_str.startswith('0x'):
                        output_value = int(output_str, 16)
                    else:
                        output_value = int(output_str)
                    
                    if controller.write_outputs(output_value):
                        print(f"✓ 输出设置成功: 0x{output_value:04X}")
                    else:
                        print("✗ 输出设置失败")
                        
                except ValueError:
                    print("输入格式错误")
            
            elif choice == "3":
                # 输出测试模式
                print("输出测试模式...")
                test_patterns = [0x0001, 0x0002, 0x0004, 0x0008, 0xFFFF, 0x0000]
                
                for pattern in test_patterns:
                    print(f"设置输出: 0x{pattern:04X}")
                    controller.write_outputs(pattern)
                    time.sleep(1)
                
                print("测试完成")
            
            elif choice == "4":
                # 监控输入
                print("监控输入变化 (按Ctrl+C停止)...")
                last_inputs = None
                
                try:
                    while True:
                        inputs = controller.read_inputs()
                        if inputs is not None and inputs != last_inputs:
                            print(f"输入变化: 0x{inputs:04X} ({inputs:016b})")
                            if last_inputs is not None:
                                changed = inputs ^ last_inputs
                                for i in range(16):
                                    if changed & (1 << i):
                                        state = "ON " if (inputs & (1 << i)) else "OFF"
                                        print(f"  输入{i}: {state}")
                            last_inputs = inputs
                        time.sleep(0.1)
                        
                except KeyboardInterrupt:
                    print("\n监控停止")
            
            elif choice == "0":
                break
            
            else:
                print("无效选择")
    
    finally:
        controller.disconnect()

if __name__ == "__main__":
    main()
