#!/usr/bin/env python3
"""
GSIO-B-EIC-1616P 正确解决方案
基于EDS文件分析，使用正确的I/O连接方法
"""

import time
import struct
from pycomm3 import CIPDriver

class GSIOController:
    """GSIO-B-EIC-1616P 正确的控制器实现"""
    
    def __init__(self, ip_address: str):
        self.ip_address = ip_address
        self.driver = None
        self.io_connection = None
        
        # 从EDS文件提取的参数
        self.INPUT_ASSEMBLY = 100
        self.OUTPUT_ASSEMBLY = 150
        self.CONFIG_ASSEMBLY = 1  # 无配置数据
        self.INPUT_SIZE = 2       # 2字节输入
        self.OUTPUT_SIZE = 2      # 2字节输出
        self.CONFIG_SIZE = 0      # 无配置
        self.RPI = 20000          # 20ms RPI (微秒)
    
    def connect(self) -> bool:
        """建立I/O连接"""
        print(f"连接到GSIO设备: {self.ip_address}")
        
        try:
            # 创建驱动实例
            self.driver = CIPDriver(self.ip_address)
            
            # 方法1: 尝试使用forward_open建立I/O连接
            print("尝试方法1: forward_open I/O连接...")
            try:
                # 构建连接参数
                connection_params = {
                    'o_t_instance': self.OUTPUT_ASSEMBLY,  # 输出Assembly
                    'o_t_size': self.OUTPUT_SIZE,
                    't_o_instance': self.INPUT_ASSEMBLY,   # 输入Assembly  
                    't_o_size': self.INPUT_SIZE,
                    'config_instance': self.CONFIG_ASSEMBLY,
                    'config_size': self.CONFIG_SIZE,
                    'rpi': self.RPI
                }
                
                # 尝试建立连接
                result = self.driver.forward_open(**connection_params)
                if result:
                    print("✓ I/O连接建立成功")
                    return True
                    
            except Exception as e:
                print(f"方法1失败: {e}")
            
            # 方法2: 尝试使用原始CIP服务
            print("尝试方法2: 原始CIP Forward Open...")
            try:
                # 构建Forward Open请求
                forward_open_data = self._build_forward_open_request()
                
                result = self.driver.generic_message(
                    service=0x54,  # Forward_Open
                    class_code=0x06,  # Connection Manager
                    instance=1,
                    request_data=forward_open_data,
                    connected=False,
                    unconnected_send=True,
                    route_path=False,
                    name='forward_open'
                )
                
                if result:
                    print("✓ 原始Forward Open成功")
                    return True
                    
            except Exception as e:
                print(f"方法2失败: {e}")
            
            # 方法3: 简化的连接尝试
            print("尝试方法3: 简化连接...")
            try:
                # 只是打开基本连接，不建立I/O连接
                self.driver.open()
                print("✓ 基本连接成功")
                return True
                
            except Exception as e:
                print(f"方法3失败: {e}")
                
            return False
            
        except Exception as e:
            print(f"连接失败: {e}")
            return False
    
    def _build_forward_open_request(self) -> bytes:
        """构建Forward Open请求数据"""
        # 基于EDS文件的Connection Manager配置
        data = bytearray()
        
        # Priority/Tick time
        data.extend(struct.pack('<B', 0x0A))  # Priority: 2, Tick time: 10ms
        
        # Timeout ticks
        data.extend(struct.pack('<B', 0xF4))  # 244 ticks
        
        # O->T Network Connection ID
        data.extend(struct.pack('<L', 0x20000001))
        
        # T->O Network Connection ID  
        data.extend(struct.pack('<L', 0x20000002))
        
        # Connection Serial Number
        data.extend(struct.pack('<H', 0x0001))
        
        # Vendor ID
        data.extend(struct.pack('<H', 32764))  # GIENSO
        
        # Originator Serial Number
        data.extend(struct.pack('<L', 0x12345678))
        
        # Connection Timeout Multiplier
        data.extend(struct.pack('<B', 0x01))
        
        # Reserved bytes
        data.extend(b'\x00\x00\x00')
        
        # O->T RPI (输出)
        data.extend(struct.pack('<L', self.RPI))
        
        # O->T Connection Parameters
        data.extend(struct.pack('<H', self.OUTPUT_SIZE))  # Connection size
        
        # T->O RPI (输入)
        data.extend(struct.pack('<L', self.RPI))
        
        # T->O Connection Parameters
        data.extend(struct.pack('<H', self.INPUT_SIZE))   # Connection size
        
        # Transport Type/Trigger
        data.extend(struct.pack('<B', 0xA3))  # Class 1, cyclic
        
        # Connection Path Size
        data.extend(struct.pack('<B', 0x04))  # 4 words
        
        # Connection Path - 指向Assembly对象
        # Output Assembly路径
        data.extend(struct.pack('<BB', 0x20, 0x04))  # Class: Assembly
        data.extend(struct.pack('<BB', 0x24, self.OUTPUT_ASSEMBLY))  # Instance: 150
        
        # Input Assembly路径  
        data.extend(struct.pack('<BB', 0x20, 0x04))  # Class: Assembly
        data.extend(struct.pack('<BB', 0x24, self.INPUT_ASSEMBLY))   # Instance: 100
        
        return bytes(data)
    
    def read_inputs_io(self) -> int:
        """通过I/O连接读取输入"""
        if not self.driver:
            print("设备未连接")
            return None
            
        try:
            # 如果有I/O连接，直接读取
            if hasattr(self.driver, 'input_data') and self.driver.input_data:
                return struct.unpack('<H', self.driver.input_data)[0]
            
            # 否则尝试显式读取
            return self.read_inputs_explicit()
            
        except Exception as e:
            print(f"读取输入失败: {e}")
            return None
    
    def read_inputs_explicit(self) -> int:
        """使用显式消息读取输入（已知可用）"""
        if not self.driver:
            return None
            
        try:
            from pycomm3 import Services, ClassCode, UINT
            
            result = self.driver.generic_message(
                service=Services.get_attribute_single,
                class_code=ClassCode.assembly,
                instance=self.INPUT_ASSEMBLY,
                attribute=3,
                data_type=UINT,
                connected=True,
                unconnected_send=False,
                route_path=False,
                name='read_inputs'
            )
            
            if result:
                return result.value
            else:
                print(f"读取失败: {result.error if result else '无响应'}")
                return None
                
        except Exception as e:
            print(f"读取异常: {e}")
            return None
    
    def write_outputs_io(self, output_value: int) -> bool:
        """通过I/O连接写入输出"""
        if not self.driver:
            print("设备未连接")
            return False
            
        try:
            # 如果有I/O连接，直接写入
            if hasattr(self.driver, 'output_data'):
                self.driver.output_data = struct.pack('<H', output_value)
                return True
            
            # 否则尝试显式写入
            return self.write_outputs_explicit(output_value)
            
        except Exception as e:
            print(f"写入输出失败: {e}")
            return False
    
    def write_outputs_explicit(self, output_value: int) -> bool:
        """使用显式消息写入输出"""
        if not self.driver:
            return False
            
        try:
            from pycomm3 import Services, ClassCode, UINT
            
            result = self.driver.generic_message(
                service=Services.set_attribute_single,
                class_code=ClassCode.assembly,
                instance=self.OUTPUT_ASSEMBLY,
                attribute=3,
                request_data=UINT.encode(output_value),
                connected=True,
                unconnected_send=False,
                route_path=False,
                name='write_outputs'
            )
            
            if result:
                return True
            else:
                print(f"写入失败: {result.error if result else '无响应'}")
                return False
                
        except Exception as e:
            print(f"写入异常: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.driver:
            try:
                self.driver.close()
            except:
                pass
            self.driver = None
            print("已断开连接")

def main():
    print("=" * 60)
    print("    GSIO-B-EIC-1616P 正确解决方案")
    print("    基于EDS文件分析的I/O连接方法")
    print("=" * 60)
    
    ip_address = input("请输入设备IP地址: ").strip()
    if not ip_address:
        print("未输入IP地址，退出")
        return
    
    controller = GSIOController(ip_address)
    
    if not controller.connect():
        print("连接失败")
        return
    
    try:
        while True:
            print("\n" + "=" * 40)
            print("选择操作:")
            print("1. 读取输入状态")
            print("2. 写入输出状态")
            print("3. 输出测试模式")
            print("4. 监控输入变化")
            print("0. 退出")
            print("=" * 40)
            
            choice = input("请选择 (0-4): ").strip()
            
            if choice == "1":
                inputs = controller.read_inputs_io()
                if inputs is not None:
                    print(f"输入状态: 0x{inputs:04X} ({inputs:016b})")
                    for i in range(16):
                        state = "ON " if (inputs & (1 << i)) else "OFF"
                        print(f"  输入{i:2d}: {state}")
            
            elif choice == "2":
                try:
                    output_str = input("请输入输出值 (十六进制，如 0x0001): ").strip()
                    if output_str.startswith('0x'):
                        output_value = int(output_str, 16)
                    else:
                        output_value = int(output_str)
                    
                    if controller.write_outputs_io(output_value):
                        print(f"✓ 输出设置成功: 0x{output_value:04X}")
                    else:
                        print("✗ 输出设置失败")
                        
                except ValueError:
                    print("输入格式错误")
            
            elif choice == "3":
                print("输出测试模式...")
                test_patterns = [0x0001, 0x0002, 0x0004, 0x0008, 0xFFFF, 0x0000]
                
                for pattern in test_patterns:
                    print(f"设置输出: 0x{pattern:04X}")
                    controller.write_outputs_io(pattern)
                    time.sleep(1)
                
                print("测试完成")
            
            elif choice == "4":
                print("监控输入变化 (按Ctrl+C停止)...")
                last_inputs = None
                
                try:
                    while True:
                        inputs = controller.read_inputs_io()
                        if inputs is not None and inputs != last_inputs:
                            print(f"输入变化: 0x{inputs:04X} ({inputs:016b})")
                            if last_inputs is not None:
                                changed = inputs ^ last_inputs
                                for i in range(16):
                                    if changed & (1 << i):
                                        state = "ON " if (inputs & (1 << i)) else "OFF"
                                        print(f"  输入{i}: {state}")
                            last_inputs = inputs
                        time.sleep(0.1)
                        
                except KeyboardInterrupt:
                    print("\n监控停止")
            
            elif choice == "0":
                break
            
            else:
                print("无效选择")
    
    finally:
        controller.disconnect()

if __name__ == "__main__":
    main()
