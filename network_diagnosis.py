#!/usr/bin/env python3
"""
网络配置诊断脚本
检查EtherNet/IP通信的网络问题
"""

import socket
import subprocess
import platform
import time
from pycomm3 import CIPDriver, Services, ClassCode, UINT

def ping_test(ip_address: str) -> bool:
    """测试ping连通性"""
    print(f"测试ping连通性到 {ip_address}...")
    
    # 根据操作系统选择ping命令
    if platform.system().lower() == "windows":
        cmd = ["ping", "-n", "4", ip_address]
    else:
        cmd = ["ping", "-c", "4", ip_address]
    
    try:
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✓ Ping测试成功")
            return True
        else:
            print("✗ Ping测试失败")
            print(result.stdout)
            return False
    except Exception as e:
        print(f"✗ Ping测试异常: {e}")
        return False

def check_network_interface():
    """检查网络接口配置"""
    print("\n检查本机网络配置...")
    
    try:
        # 获取本机IP地址
        hostname = socket.gethostname()
        local_ip = socket.gethostbyname(hostname)
        print(f"本机主机名: {hostname}")
        print(f"本机IP地址: {local_ip}")
        
        # 检查是否在同一网段
        if local_ip.startswith("192.168.100."):
            print("✓ 本机IP在192.168.100.x网段")
        else:
            print("⚠ 本机IP不在192.168.100.x网段，可能存在路由问题")
            
    except Exception as e:
        print(f"✗ 网络接口检查异常: {e}")

def test_tcp_connection_detailed(ip_address: str):
    """详细的TCP连接测试"""
    print(f"\n详细TCP连接测试到 {ip_address}:44818...")
    
    try:
        # 测试连接建立
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(5)
        
        start_time = time.time()
        result = sock.connect_ex((ip_address, 44818))
        connect_time = time.time() - start_time
        
        if result == 0:
            print(f"✓ TCP连接成功，耗时: {connect_time:.3f}秒")
            
            # 测试数据发送
            try:
                # 发送一个简单的EtherNet/IP List Identity请求
                list_identity_request = bytes([
                    0x63, 0x00,  # Command: List Identity
                    0x00, 0x00,  # Length
                    0x00, 0x00, 0x00, 0x00,  # Session Handle
                    0x00, 0x00, 0x00, 0x00,  # Status
                    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,  # Sender Context
                    0x00, 0x00, 0x00, 0x00   # Options
                ])
                
                sock.send(list_identity_request)
                print("✓ 数据发送成功")
                
                # 尝试接收响应
                sock.settimeout(2)
                response = sock.recv(1024)
                if response:
                    print(f"✓ 接收到响应，长度: {len(response)}字节")
                else:
                    print("⚠ 未接收到响应")
                    
            except socket.timeout:
                print("⚠ 接收响应超时")
            except Exception as e:
                print(f"⚠ 数据通信异常: {e}")
                
        else:
            print(f"✗ TCP连接失败，错误代码: {result}")
            
        sock.close()
        
    except Exception as e:
        print(f"✗ TCP连接测试异常: {e}")

def test_read_write_timing(ip_address: str):
    """测试读写操作的时序"""
    print(f"\n测试读写操作时序...")
    
    try:
        with CIPDriver(ip_address) as plc:
            # 测试连续读取
            print("测试连续读取...")
            for i in range(3):
                start_time = time.time()
                result = plc.generic_message(
                    service=Services.get_attribute_single,
                    class_code=ClassCode.assembly,
                    instance=100,
                    attribute=3,
                    data_type=UINT,
                    connected=True,
                    unconnected_send=False,
                    route_path=False,
                    name=f'timing_read_{i}'
                )
                read_time = time.time() - start_time
                
                if result:
                    print(f"  读取{i+1}: 成功，耗时{read_time:.3f}秒，数据: 0x{result.value:04X}")
                else:
                    print(f"  读取{i+1}: 失败，耗时{read_time:.3f}秒")
                
                time.sleep(0.1)
            
            # 测试读取后立即写入
            print("\n测试读取后立即写入...")
            
            # 先读取
            read_result = plc.generic_message(
                service=Services.get_attribute_single,
                class_code=ClassCode.assembly,
                instance=100,
                attribute=3,
                data_type=UINT,
                connected=True,
                unconnected_send=False,
                route_path=False,
                name='read_before_write'
            )
            
            if read_result:
                print(f"读取成功: 0x{read_result.value:04X}")
                
                # 立即写入
                start_time = time.time()
                write_result = plc.generic_message(
                    service=Services.set_attribute_single,
                    class_code=ClassCode.assembly,
                    instance=150,
                    attribute=3,
                    request_data=UINT.encode(0x0001),
                    connected=True,
                    unconnected_send=False,
                    route_path=False,
                    name='write_after_read'
                )
                write_time = time.time() - start_time
                
                if write_result:
                    print(f"✓ 写入成功，耗时{write_time:.3f}秒")
                else:
                    print(f"✗ 写入失败，耗时{write_time:.3f}秒，错误: {write_result.error if write_result else '无响应'}")
            
    except Exception as e:
        print(f"✗ 时序测试异常: {e}")

def main():
    print("=" * 60)
    print("    EtherNet/IP 网络配置诊断工具")
    print("=" * 60)
    
    ip_address = input("请输入设备IP地址: ").strip()
    if not ip_address:
        print("未输入IP地址，退出")
        return
    
    print(f"\n开始网络诊断: {ip_address}")
    
    # 1. 检查本机网络配置
    check_network_interface()
    
    # 2. Ping测试
    ping_ok = ping_test(ip_address)
    
    # 3. 详细TCP连接测试
    test_tcp_connection_detailed(ip_address)
    
    # 4. 读写时序测试
    test_read_write_timing(ip_address)
    
    # 5. 网络配置建议
    print("\n" + "=" * 60)
    print("网络配置建议:")
    print("=" * 60)
    
    if ping_ok:
        print("✓ 基本网络连通性正常")
        print("\n推荐的直连配置:")
        print("设备端:")
        print("  IP: ***************")
        print("  子网掩码: *************")
        print("  网关: 0.0.0.0 (留空)")
        print("\nPC端:")
        print("  IP: ***************")
        print("  子网掩码: *************")
        print("  网关: 0.0.0.0 (留空)")
        
        print("\n如果写入仍然失败，可能的原因:")
        print("1. 设备输出功能被硬件开关禁用")
        print("2. 设备需要特定的初始化序列")
        print("3. 设备固件版本问题")
        print("4. 需要使用厂商专用工具进行配置")
    else:
        print("✗ 基本网络连通性异常")
        print("请检查:")
        print("1. 网线连接是否正常")
        print("2. 网卡是否启用")
        print("3. IP地址配置是否正确")
        print("4. 防火墙是否阻止了通信")

if __name__ == "__main__":
    main()
