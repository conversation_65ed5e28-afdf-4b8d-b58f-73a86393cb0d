#!/usr/bin/env python3
"""
GSIO-B-EIC-1616P 连接测试脚本
用于验证设备连接和基本功能
"""

import sys
import time
from pycomm3 import CIPDriver
import struct


def test_device_connection(ip_address: str) -> bool:
    """测试设备连接"""
    print(f"正在测试连接到设备: {ip_address}")
    
    try:
        with CIPDriver(ip_address) as plc:
            # 获取设备信息
            device_info = plc.get_device_info()
            
            if device_info:
                print("✓ 连接成功!")
                print(f"设备信息: {device_info}")
                
                # 验证厂商和产品代码
                if hasattr(device_info, 'vendor') and device_info.vendor == 32764:
                    print("✓ 厂商代码匹配 (GIENSO)")
                else:
                    print("⚠ 厂商代码不匹配，可能不是GSIO设备")
                
                if hasattr(device_info, 'product_code') and device_info.product_code == 10006:
                    print("✓ 产品代码匹配 (GSIO-B-EIC-1616P)")
                else:
                    print("⚠ 产品代码不匹配")
                
                return True
            else:
                print("✗ 无法获取设备信息")
                return False
                
    except Exception as e:
        print(f"✗ 连接失败: {e}")
        return False


def test_assembly_access(ip_address: str) -> bool:
    """测试Assembly访问"""
    print("\n正在测试Assembly访问...")
    
    try:
        with CIPDriver(ip_address) as plc:
            # 测试读取输入Assembly (100)
            print("测试读取输入Assembly (100)...")
            input_result = plc.generic_message(
                service=0x0E,        # Get_Attribute_Single
                class_code=0x04,     # Assembly Object
                instance=100,        # Input Assembly
                attribute=0x03,      # Data attribute
                request_data=b''
            )
            
            if input_result and len(input_result.data) >= 2:
                input_value = struct.unpack('<H', input_result.data[:2])[0]
                print(f"✓ 输入Assembly读取成功: 0x{input_value:04X} ({input_value:016b})")
            else:
                print("✗ 输入Assembly读取失败")
                return False
            
            # 测试写入输出Assembly (150)
            print("测试写入输出Assembly (150)...")
            test_output = 0x0001  # 只点亮输出0
            output_data = struct.pack('<H', test_output)
            
            output_result = plc.generic_message(
                service=0x10,        # Set_Attribute_Single
                class_code=0x04,     # Assembly Object
                instance=150,        # Output Assembly
                attribute=0x03,      # Data attribute
                request_data=output_data
            )
            
            if output_result and output_result.service == 0x90:
                print(f"✓ 输出Assembly写入成功: 0x{test_output:04X}")
                
                # 延时后关闭输出
                time.sleep(1)
                plc.generic_message(
                    service=0x10,
                    class_code=0x04,
                    instance=150,
                    attribute=0x03,
                    request_data=struct.pack('<H', 0)
                )
                print("✓ 输出已关闭")
                
            else:
                print("✗ 输出Assembly写入失败")
                return False
            
            return True
            
    except Exception as e:
        print(f"✗ Assembly访问异常: {e}")
        return False


def test_io_operations(ip_address: str) -> bool:
    """测试IO操作"""
    print("\n正在测试IO操作...")
    
    try:
        with CIPDriver(ip_address) as plc:
            print("执行输出测试序列...")
            
            # 测试模式: 逐个点亮输出
            for i in range(8):  # 只测试前8个输出
                output_value = 1 << i
                output_data = struct.pack('<H', output_value)
                
                result = plc.generic_message(
                    service=0x10,
                    class_code=0x04,
                    instance=150,
                    attribute=0x03,
                    request_data=output_data
                )
                
                if result and result.service == 0x90:
                    print(f"  输出{i}: ON")
                else:
                    print(f"  输出{i}: 设置失败")
                    
                time.sleep(0.3)
            
            # 关闭所有输出
            plc.generic_message(
                service=0x10,
                class_code=0x04,
                instance=150,
                attribute=0x03,
                request_data=struct.pack('<H', 0)
            )
            print("✓ 所有输出已关闭")
            
            # 读取输入状态
            print("\n读取当前输入状态:")
            input_result = plc.generic_message(
                service=0x0E,
                class_code=0x04,
                instance=100,
                attribute=0x03,
                request_data=b''
            )
            
            if input_result and len(input_result.data) >= 2:
                input_value = struct.unpack('<H', input_result.data[:2])[0]
                print(f"输入状态: 0x{input_value:04X}")
                
                for i in range(16):
                    state = "ON " if (input_value & (1 << i)) else "OFF"
                    print(f"  输入{i:2d}: {state}")
            
            return True
            
    except Exception as e:
        print(f"✗ IO操作异常: {e}")
        return False


def main():
    """主测试程序"""
    print("=== GSIO-B-EIC-1616P 连接测试 ===")
    
    # 获取设备IP地址
    if len(sys.argv) > 1:
        device_ip = sys.argv[1]
    else:
        device_ip = input("请输入设备IP地址 (默认: *************): ").strip()
        if not device_ip:
            device_ip = "*************"
    
    print(f"目标设备: {device_ip}")
    print("-" * 50)
    
    # 执行测试
    tests = [
        ("设备连接测试", lambda: test_device_connection(device_ip)),
        ("Assembly访问测试", lambda: test_assembly_access(device_ip)),
        ("IO操作测试", lambda: test_io_operations(device_ip))
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✓ {test_name} 通过")
            else:
                print(f"✗ {test_name} 失败")
                
        except KeyboardInterrupt:
            print(f"\n{test_name} 被用户中断")
            break
        except Exception as e:
            print(f"✗ {test_name} 异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果汇总
    print(f"\n{'='*20} 测试结果汇总 {'='*20}")
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "通过" if result else "失败"
        symbol = "✓" if result else "✗"
        print(f"{symbol} {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{total} 项测试通过")
    
    if passed == total:
        print("🎉 所有测试通过! 设备连接正常，可以开始使用控制程序。")
        return 0
    else:
        print("⚠ 部分测试失败，请检查:")
        print("  1. 设备IP地址是否正确")
        print("  2. 网络连接是否正常")
        print("  3. 设备是否正常运行")
        print("  4. 防火墙是否阻止EtherNet/IP通信")
        return 1


if __name__ == "__main__":
    try:
        exit_code = main()
        sys.exit(exit_code)
    except KeyboardInterrupt:
        print("\n\n测试被用户中断")
        sys.exit(1)
