#!/usr/bin/env python3
"""
GSIO-B-EIC-1616P 快速启动脚本
提供简单的菜单界面来选择不同的功能
"""

import os
import sys
import subprocess


def check_dependencies():
    """检查依赖包"""
    try:
        import pycomm3
        print("✓ pycomm3 已安装")
        return True
    except ImportError:
        print("✗ pycomm3 未安装")
        install = input("是否现在安装? (y/n): ").lower()
        if install == 'y':
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "pycomm3"])
                print("✓ pycomm3 安装成功")
                return True
            except subprocess.CalledProcessError:
                print("✗ pycomm3 安装失败")
                return False
        return False


def get_device_ip():
    """获取设备IP地址"""
    ip = input("请输入GSIO设备IP地址 (默认: *************): ").strip()
    if not ip:
        ip = "*************"
    return ip


def run_script(script_name, device_ip=None):
    """运行指定脚本"""
    if not os.path.exists(script_name):
        print(f"✗ 脚本文件不存在: {script_name}")
        return
    
    try:
        if device_ip:
            subprocess.run([sys.executable, script_name, device_ip])
        else:
            subprocess.run([sys.executable, script_name])
    except KeyboardInterrupt:
        print(f"\n{script_name} 被用户中断")
    except Exception as e:
        print(f"✗ 运行 {script_name} 时出错: {e}")


def main():
    """主菜单"""
    print("=" * 60)
    print("    GSIO-B-EIC-1616P EtherNet/IP 控制程序")
    print("    基于pycomm3库的16输入16输出IO模块控制")
    print("=" * 60)
    
    # 检查依赖
    if not check_dependencies():
        print("请先安装依赖包后再运行程序")
        return
    
    # 获取设备IP
    device_ip = get_device_ip()
    print(f"目标设备: {device_ip}")
    
    while True:
        print("\n" + "=" * 40)
        print("请选择要运行的程序:")
        print("1. 连接测试 (推荐首次使用)")
        print("2. 简单IO控制示例")
        print("3. 完整控制程序")
        print("4. 工业应用示例")
        print("5. 修改设备IP地址")
        print("0. 退出")
        print("=" * 40)
        
        choice = input("请输入选择 (0-5): ").strip()
        
        if choice == "1":
            print("\n启动连接测试...")
            run_script("test_connection.py", device_ip)
            
        elif choice == "2":
            print("\n启动简单IO控制示例...")
            # 需要修改脚本中的IP地址
            modify_ip_in_script("simple_gsio_example.py", device_ip)
            run_script("simple_gsio_example.py")
            
        elif choice == "3":
            print("\n启动完整控制程序...")
            modify_ip_in_script("gsio_eip_controller.py", device_ip)
            run_script("gsio_eip_controller.py")
            
        elif choice == "4":
            print("\n启动工业应用示例...")
            modify_ip_in_script("industrial_example.py", device_ip)
            run_script("industrial_example.py")
            
        elif choice == "5":
            device_ip = get_device_ip()
            print(f"设备IP已更新为: {device_ip}")
            
        elif choice == "0":
            print("退出程序")
            break
            
        else:
            print("无效选择，请重新输入")


def modify_ip_in_script(script_name, new_ip):
    """修改脚本中的IP地址"""
    if not os.path.exists(script_name):
        return
    
    try:
        with open(script_name, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找并替换IP地址
        import re
        pattern = r'DEVICE_IP\s*=\s*["\'][\d.]+["\']'
        replacement = f'DEVICE_IP = "{new_ip}"'
        
        if re.search(pattern, content):
            new_content = re.sub(pattern, replacement, content)
            
            with open(script_name, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print(f"✓ 已更新 {script_name} 中的设备IP为 {new_ip}")
        
    except Exception as e:
        print(f"⚠ 更新 {script_name} 中的IP地址失败: {e}")


if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n程序异常: {e}")
