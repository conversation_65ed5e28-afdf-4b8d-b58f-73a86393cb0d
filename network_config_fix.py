#!/usr/bin/env python3
"""
网络配置修复工具
解决GSIO设备网络配置问题
"""

import subprocess
import socket
import time
from typing import Optional

def check_ping(ip: str, timeout: int = 3) -> bool:
    """检查设备是否可ping通"""
    try:
        result = subprocess.run(
            ['ping', '-c', '1', '-W', str(timeout * 1000), ip],
            capture_output=True,
            text=True
        )
        return result.returncode == 0
    except:
        return False

def check_tcp_connection(ip: str, port: int = 44818, timeout: int = 3) -> bool:
    """检查TCP连接"""
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        result = sock.connect_ex((ip, port))
        sock.close()
        return result == 0
    except:
        return False

def get_network_info():
    """获取网络配置信息"""
    print("当前网络配置:")
    
    try:
        # 获取路由表
        result = subprocess.run(['route', '-n', 'get', 'default'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("默认路由:")
            print(result.stdout)
        
        # 获取网络接口信息
        result = subprocess.run(['ifconfig'], capture_output=True, text=True)
        if result.returncode == 0:
            lines = result.stdout.split('\n')
            for line in lines:
                if 'inet 192.168.100' in line:
                    print(f"发现192.168.100网段: {line.strip()}")
                    
    except Exception as e:
        print(f"获取网络信息失败: {e}")

def diagnose_network_issue(device_ip: str):
    """诊断网络问题"""
    print(f"\n诊断设备 {device_ip} 的网络连接...")
    
    # 1. 检查ping
    print("1. 检查ping连通性...")
    if check_ping(device_ip):
        print("✓ Ping成功")
    else:
        print("✗ Ping失败")
        return False
    
    # 2. 检查EtherNet/IP端口
    print("2. 检查EtherNet/IP端口 (44818)...")
    if check_tcp_connection(device_ip, 44818):
        print("✓ TCP 44818端口连接成功")
    else:
        print("✗ TCP 44818端口连接失败")
    
    # 3. 检查其他常用端口
    common_ports = [80, 443, 502, 2222]
    print("3. 检查其他常用端口...")
    for port in common_ports:
        if check_tcp_connection(device_ip, port, 1):
            print(f"✓ 端口 {port} 开放")
    
    return True

def suggest_network_fixes():
    """建议网络修复方案"""
    print("\n" + "=" * 50)
    print("网络配置修复建议:")
    print("=" * 50)
    
    print("\n1. 设备端配置 (通过设备Web界面或配置工具):")
    print("   - IP地址: ***************")
    print("   - 子网掩码: *************")
    print("   - 网关: 0.0.0.0 (留空或设为0.0.0.0)")
    print("   - DNS: 0.0.0.0 (留空)")
    
    print("\n2. PC端配置:")
    print("   - IP地址: ***************")
    print("   - 子网掩码: *************")
    print("   - 网关: 留空 (不设置)")
    print("   - DNS: 留空")
    
    print("\n3. 物理连接:")
    print("   - 使用直连网线连接PC和设备")
    print("   - 确保网线正常工作")
    print("   - 检查网卡指示灯状态")
    
    print("\n4. 防火墙设置:")
    print("   - 临时关闭防火墙测试")
    print("   - 或添加端口44818的例外规则")
    
    print("\n5. macOS网络配置命令:")
    print("   sudo route delete default  # 删除默认路由")
    print("   sudo route add -net *************/24 -interface en0  # 添加静态路由")

def test_pycomm3_connection(device_ip: str):
    """测试pycomm3连接"""
    print(f"\n测试pycomm3连接到 {device_ip}...")
    
    try:
        from pycomm3 import CIPDriver
        
        print("创建CIP驱动...")
        driver = CIPDriver(device_ip)
        
        print("尝试连接...")
        driver.open()
        
        print("✓ pycomm3连接成功!")
        
        # 尝试读取设备信息
        try:
            print("读取设备标识...")
            result = driver.get_device_info()
            if result:
                print(f"设备信息: {result}")
        except Exception as e:
            print(f"读取设备信息失败: {e}")
        
        driver.close()
        return True
        
    except ImportError:
        print("✗ pycomm3未安装")
        print("安装命令: pip install pycomm3")
        return False
    except Exception as e:
        print(f"✗ pycomm3连接失败: {e}")
        return False

def main():
    print("=" * 60)
    print("    GSIO设备网络配置修复工具")
    print("=" * 60)
    
    # 获取当前网络信息
    get_network_info()
    
    # 设备IP
    device_ip = "***************"
    
    # 诊断网络问题
    if diagnose_network_issue(device_ip):
        # 测试pycomm3连接
        test_pycomm3_connection(device_ip)
    
    # 显示修复建议
    suggest_network_fixes()
    
    print("\n" + "=" * 60)
    print("修复步骤:")
    print("1. 按照上述建议配置网络")
    print("2. 重启网络接口或重启设备")
    print("3. 再次运行此脚本验证")
    print("4. 使用 gsio_correct_solution.py 测试I/O功能")
    print("=" * 60)

if __name__ == "__main__":
    main()
