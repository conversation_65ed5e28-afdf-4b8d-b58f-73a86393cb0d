# GSIO-B-EIC-xxx 产品使用手册

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/2988626a318e86e5f01c0a15223136372a6bdf5aec8a6b0757b7823b0bae5591.jpg)

Ver 1.0

# 目录

# 一、产品简介

1.1 电气规格

# 1.2 接线图

1.2.1 GSIO- B- EIC- 32DI 接线图 3  1.2.2 GSIO- B- EIC- 32DON 接线图 4  1.2.3 GSIO- B- EIC- 1616P 接线图 5  1.2.4 GSIO- B- EIC- 1616N 接线图 6  1.2.5 GSIO- B- EIC- 4AIH 接线图 7  1.2.6 GSIO- B- EIC- 4AOH 接线图 8  1.2.7 GSIO- B- EIC- 8AIH 接线图 9  1.2.8 GSIO- B- EIC- 8AOH 接线图 10  1.2.9 GSIO- B- EIC- 8AMH 接线图 11

# 二、产品模块说明

# 2.1 指示灯说明

2.1.1 数字量模块指示灯说明 12  2.1.2 模拟量模块指示灯说明 12

2.2 接线端子说明

2.3 拨码开关设置说明

2.4 网页参数说明

# 三、使用示例

3.1 GSIO- B- EIC- 1616N 模块与欧姆龙 NX102- 9000 通讯示例 16

3.1.1 通讯连接示意图 16

3.1.2 硬件配置 16

3.1.3 GSIO- B- EIC- 1616N 模块 EIP 参数设置 17

3.1.4 添加 EDS 文件 19

3.1.5 建立连接 20附录Ⅰ 耦合器规格参数表 27

<table><tr><td>手册版本</td><td>说明</td></tr><tr><td>V1.0</td><td>初始版本</td></tr></table>

# 一、产品简介

一、产品简介GSIO-B-EIC-xxx紧凑型立式模块，支持EtherNet/IP总线通信协议，本体带IO，带8位拨码设置IP地址，支持DN35安装。

# 1.1 电气规格

<table><tr><td>型号</td><td>GSIO-B-EIC-xxx</td></tr><tr><td>产品概述</td><td>2个 RJ45 接口，24VDC 供电
性能稳定、抗干扰性能强</td></tr><tr><td colspan="2">技术规格</td></tr><tr><td>订货号</td><td>GSIO-B-EIC-xxx</td></tr><tr><td>电气接口</td><td>2*RJ45</td></tr><tr><td>工作电源</td><td>24VDC</td></tr><tr><td>电缆长度（非屏蔽）</td><td>100m</td></tr><tr><td>是否连接CPU</td><td>否（独立作为从站）</td></tr><tr><td>支持协议</td><td>EtherNet/IP 从站</td></tr><tr><td>本体自带IO数量</td><td>有，详见附录Ⅰ</td></tr><tr><td>支持扩展IO 模块数量</td><td>不支持扩展</td></tr><tr><td colspan="2">从站设置</td></tr><tr><td>地址设置</td><td>网页配置，或者拨码配置</td></tr><tr><td>每段最大站数</td><td>255（具体支持站数由主站决定）</td></tr><tr><td colspan="2">隔离</td></tr></table>

<table><tr><td>通道与总线之间</td><td>有</td></tr><tr><td>电源到总线</td><td>有</td></tr><tr><td>系统电源诊断和警告</td><td>支持</td></tr><tr><td>工作环境</td><td>工作温度：-25~60℃；相对湿度：5%~90%(无凝露)</td></tr><tr><td>尺寸（长×宽×高）</td><td>27×94×114mm</td></tr></table>

# 1.2 接线图

# 1.2.1 GSIO-B-EIC-32DI 接线图

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/efd6cedcd823de5747d3f083472077fcd5a6ed8ed50d5578fcf5bc77285c7b10.jpg)  
GSIO-B-EIC-32DI 接线图

# 1.2.2 GSIO-B-EIC-32DON 接线图

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/8d708b2d39cc5b1bd389168b0401083556c61b146d2859fd0f5f3d00fc94d2c9.jpg)

# 1.2.3 GSIO-B-EIC-1616P 接线图

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/29a64215ee30b4d93b037e7327c37439c99e7056f4f0b727d7b11e14e124dc32.jpg)

# 1.2.4 GSIO-B-EIC-1616N 接线图

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/716dbafcf138e5ab6862fe85adae4f34b0aed98e242f3766b5595cea659af78e.jpg)  
GSIO-B-EIC-1616N 接线图

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/2efd417c2cb69d664ead31b7e28e7a07dc984f46d79487acc876340bc2c485d1.jpg)

# 1.2.5 GSIO-B-EIC-4AIH 接线图

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/4ed300c6ea7c5a0bade8862634f24208baaa2ce303cd157e16497812b3046b70.jpg)

# 1.2.6 GSIO-B-EIC-4AOH 接线图

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/003c1002e48927567f4a5af05356ffe12f093fe693f3a9523c72da37e697209b.jpg)

# 1.2.7 GSIO-B-EIC-8AIH 接线图

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/2c12be6180a7ce135b0504b0719c48bdffef252640660dcc0c257539a4381b27.jpg)

# 1.2.8 GSIO-B-EIC-8AOH 接线图

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/2d17966d2db117e433b58ae140d2c3cf1f183d96c93ab833fc851c7a44be8d9d.jpg)

# 1.2.9 GSIO-B-EIC-8AMH 接线图

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/207ed3415524cd353c8bd9f6b2db3f252e5e45f55c9244e66ba3f8e1614c16e1.jpg)

# 二、产品模块说明

# 2.1 指示灯说明

# 2.1.1 数字量模块指示灯说明

<table><tr><td>指示灯</td><td>说明</td></tr><tr><td>PWR</td><td>电源指示灯，正常供电时指示灯亮，异常时熄灭。</td></tr><tr><td>SF</td><td>长亮：通信异常；
熄灭：通信正常；</td></tr><tr><td>NET</td><td>熄灭：通信异常；
长亮：通讯正常；</td></tr></table>

# 2.1.2 模拟量模块指示灯说明

<table><tr><td>指示灯</td><td>说明</td></tr><tr><td>PWR</td><td>电源指示灯，正常供电时指示灯亮，异常时熄灭。</td></tr><tr><td>NET</td><td>熄灭：通信异常；
长亮：通讯正常；</td></tr></table>

# 2.2 接线端子说明

<table><tr><td>接线端子</td><td>说明</td></tr><tr><td>RJ45</td><td>两个 RJ45 口用于 EtherNet/IP 通讯</td></tr><tr><td>RJ45</td><td>两个 RJ45 口用于 EtherNet/IP 通讯</td></tr></table>

<table><tr><td>L+</td><td>IO端电源L+,与可插拔IO端子上的L+相连，给IO端的数字量输出供电，电压范围：20.4~28.8V DC。</td></tr><tr><td>M</td><td>IO端电源M,与可插拔IO端子上的M相连。</td></tr><tr><td>EARTH</td><td>接地端</td></tr></table>

# 2.3 拨码开关设置说明

<table><tr><td>拨码开关</td><td>说明</td></tr><tr><td>拨码开关</td><td>(1)所有拨码都拨为OFF时，耦合器进行EIP通讯使用的IP地址通过网页进行配置，设置范围XXX.XXX.XXX.1～XXX.XXX.XXX.254。此处的“XXX.XXX.XXX.”为实际使用中接入的网段。
(2)当拨码开关拨到ON时，则耦合器进行EIP通讯使用的IP地址的最后一位为拨码开关设定的值，网段以网页设置为准，例如：网页上设置IP地址***************；把拨码开关1、2拨到ON，其他为OFF，此时耦合器的IP地址为*************。
IP地址=SW1×20+SW2×21+...+SW8×27，
IP地址范围：XXX.XXX.XXX.1～XXX.XXX.XXX.254。
拨码开关设置后，模块需断电重启才能生效。</td></tr></table>

# 2.4 网页参数说明

GSIO- B- EIC- xxx模块的默认访问网页的IP为*************，可以使用次IP登录到网页参数配置页面进行参数配置，详细操作见本手册章节“3.1.3 GSIO- B- EIC- xxx模块EIP参数设置”，网页参数如下图所：

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/e64ce93b28f962ee34d0f91b7c2fc0273ae24e1f09fc1402e6fef4090b68efc4.jpg)

IP地址：设置GSIO- B- EIC- xxx耦合器与所连接的控制器通讯用，需要与控制器的IP地址在同一网段中。

网关地址：设置GSIO- B- EIC- xxx耦合器的网关。

子网掩码：设置GSIO- B- EIC- xxx耦合器的掩码。

MAC地址：设置GSIO- B- EIC- xxx耦合器的MAC地址，在同一个网络中存在多个设备时MAC地址不能相同，否则通讯异常。

通讯超时时间：设置GSIO- B- EIC- xxx耦合器与控制器通讯断开后，GSIO- B- EIC- xxx数字量、模拟量输出通道输出清零或者保持，共4个设置项，分别为：200ms、500ms、1s、输出保存。设置为200ms、500ms、1s时，通讯断开超过所设置的时间后GSIO- B- EIC- xxx数字量、模拟量输出通道输出清零；设置为“输出保存”，通讯断开后GSIO- B- EIC- xxx数字量、模拟量输出通道保存输出。

主机STOP清除：主机（即控制GSIO- B- EIC- xxx的PLC）由RUN变为STOP时，GSIO- B- EIC- xxx的数字量、模拟量输出通道的执行动作设置（注意：目前只有基恩士

# PLC支持此项功能)。

是：主机由RUN变为STOP时GSIO- B- EIC- xxx后的数字量、模拟量输出通道输出清零；

否：主机由RUN变为STOP时GSIO- B- EIC- xxx后的数字量、模拟量输出通道输出保持；

# 三、使用示例

# 3.1 GSIO-B-EIC-1616N 模块与欧姆龙 NX102-9000 通讯示例

本示例简单介绍 GSIO- B- EIC- 1616N 模块与欧姆龙 NX102- 9000 进行 EtherNet /IP 通讯，实现 NX102- 9000 对 GSIO- B- EIC- 1616N 模块的控制。

# 3.1.1 通讯连接示意图

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/f0ee094fd2639d62ea52f9df0e79dc61894fe1543f56cfd01fe9e16fadc90b34.jpg)

# 3.1.2 硬件配置

硬件配置如下表所示：

<table><tr><td>硬件</td><td>数量</td><td>备注</td></tr><tr><td>编程电脑</td><td>1台</td><td>安装欧姆龙 Sysmac Studio 1.47</td></tr><tr><td>NX102-9000</td><td>1个</td><td>欧姆龙控制器</td></tr><tr><td>GSIO-B-EIC-1616N</td><td>1个</td><td>耦合器</td></tr><tr><td>24V开关电源</td><td>1个</td><td></td></tr><tr><td>网线</td><td>若干</td><td></td></tr><tr><td>电源线、信号线</td><td>若干</td><td></td></tr></table>

# 3.1.3 GSIO-B-EIC-1616N 模块 EIP 参数设置

设置电脑本地 IP 地址，因为 EIP 模块的默认访问网页的 IP 为 *************，模块初始使用时，本地连接的 IP 与模块 IP 必须在同一网段才能实现直连的正常通讯，故需更改电脑本地连接的 IP 地址；

注：耦合器出厂默认两个 IP 地址，*************：用于访问网页，且任何时候都能使用此 IP 访问（包括忘记 IP 时）；***************：用于与 CPU 通讯的 IP，可通过在网页上修改。

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/d18575b8d738e8bd8f85a0596fd4542e585a7e84bfe9a2ac3cf980750e6f30ec.jpg)

配置好电脑的 IP 地址后，电脑上打开浏览器，在浏览器的地址栏中输入 *************:2250，登录到网页参数配置页面，GSIO- B- EIC- xxx 上 RJ45 网口的 IP 地址固定为 *************，默认的用户名为 admin，密码为 admin，如下图所示：

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/425edba3e67f1816629c7a6edaafb7388d8bd1e3758e20a938c01ba290cdd7d7.jpg)

然后回车进入到 GSIO- B- EIC- xxx 耦合器的网页参数设置页面，如下图所示：

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/3cc9e9249fb8c24c4e09587e5ec633e089b5d484e2a0e236860eacae648b4784.jpg)

# 3.1.4 添加 EDS 文件

打开 Sysmac Studio 软件，创建一个工程，在菜单栏找到“工具（T）”，按照下图步骤添加 EDS 文件：

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/c663987ad12ca4834ccc69ce62e7ec245668302de293d8e256d67c064e987b8e.jpg)

# 3.1.5 建立连接

# 3.1.5.1GSIO-B-EIC-1616N数据配置说明

本示例使用的模块组合为GSIO- B- EIC- 1616N使用到的字节数如下表所示：

<table><tr><td colspan="2">模块型号</td><td>占用字节数</td></tr><tr><td>输入类型</td><td>GSIO-B-EIC-1616N</td><td>2</td></tr><tr><td>输出类型</td><td>GSIO-B-EIC-1616N</td><td>2</td></tr></table>

打开软件中“全局变量”得界面，添加两个数组变量，一个用于读耦合器的输入，一个用于写耦合器的输出，数组长度需要与添加耦合器时候设置的输入和输出的长度一致：

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/b65fda55a741367d80cacc6414347f0cb288a98ff81ab7b6b4bcd48b58829ed6.jpg)

# *******添加GSIO-B-EIC-1616N设备

打开“Sysmac Studio”编程软件，选择相应的CPU型号，设置CPU的IP地址：

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/3c5e2a15cc02c30f3a9c4058abe4fba184a2b919c3ec891c87cde565210ae442.jpg)

打开“工具”  $\rightarrow$  “打开EtherNet/IP连接设置”，配置EtherNet/IP连接设置：

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/ac1de56778c55d7d1d3a3a9de31f61e20bc0b761c4edcf82ba75b7e2131d5655.jpg)

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/932510574d6a130a01cfdd8d9357a0ffda943454c7525f7338deaffc3bc082b8.jpg)

耦合器添加成功后，需要配置输入、输出数据长度，以及填写IO个数：

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/80f420e20a5fe522fbd1ede38d086a0ac8d5edcc0fcb8623ed177de1b45258f1.jpg)

# *******关联变量

将全局变量中的变量注册到标签组：

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/045181f7ddaf7bd302548f976edac8f98fb0be1fe3da871aefbcf82062a4bccf.jpg)

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/db9b13ba348ca7561a38f17f1ea35c502434575bc600fe576a9599cc1a3dfe24.jpg)

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/0dacdcea5fb3d1b599d9e401f82f025e1d7cff6e09d62eda06dc4294a70eb484.jpg)

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/6ba8d24f96baebee711f1f83461478db9e97ed27e1091937cada9a5a83ff1423.jpg)

# 3.1.5.4 在线监控数据

监控结果：

![](https://cdn-mineru.openxlab.org.cn/result/2025-07-31/9c7e394e-f0eb-47b2-8cd2-4ca5f5f89469/b0771c7c7604c70c1850ea653af96755c6cca589bbad979c058ff05d81fdaec1.jpg)

<table><tr><td colspan="7">监视工程1</td></tr><tr><td>设备名称</td><td>名称</td><td>在线值</td><td>修改</td><td>注释</td><td>数据类型</td><td></td></tr><tr><td rowspan="3">new_Controller_0</td><td>Input[1..2]</td><td></td><td></td><td></td><td>ARRAY[1..2]</td><td></td></tr><tr><td>Input[1]</td><td>00</td><td></td><td></td><td>BYTE</td><td></td></tr><tr><td>Input[2]</td><td>00</td><td></td><td></td><td>Byte</td><td></td></tr><tr><td rowspan="3">new_Controller_0</td><td>Output[1..2]</td><td></td><td></td><td></td><td>ARRAY[1..2]</td><td></td></tr><tr><td>Output[1]</td><td>FF</td><td>FF</td><td></td><td>Byte</td><td></td></tr><tr><td>Output[2]</td><td>FF</td><td>FF</td><td></td><td>Byte</td><td></td></tr></table>

附录I 耦合器规格参数表  

<table><tr><td>耦合器规格</td><td>型号</td></tr><tr><td>2个RJ45口，24VDC供电，带8位拨码设置站IP地址，本体集成16DI（NPN/PNP）和16DO（NPN输出），不支持扩展</td><td>GSIO-B-EIC-1616N</td></tr><tr><td>2个RJ45口，24VDC供电，带8位拨码设置站IP地址，本体集成16DI（NPN/PNP）和16DO（PNP输出），不支持扩展</td><td>GSIO-B-EIC-1616P</td></tr><tr><td>2个RJ45口，24VDC供电，带8位拨码设置站IP地址，本体集成16DI（NPN/PNP输入）和16DO（NPN输入/NPN输出可配置，2路一组配置）</td><td>GSIO-B-EIC-1616S</td></tr><tr><td>2个RJ45口，24VDC供电，带8位拨码设置站IP地址，本体集成32DI（NPN/PNP输入）</td><td>GSIO-B-EIC-32DI</td></tr><tr><td>2个RJ45口，24VDC供电，带8位拨码设置站IP地址，本体集成32DO（NPN输出）</td><td>GSIO-B-EIC-32DON</td></tr><tr><td>2个RJ45口，24VDC供电，带8位拨码设置站IP地址，本体集成32DO（PNP输出）</td><td>GSIO-B-EIC-32DOP</td></tr><tr><td>2个RJ45口，24VDC供电，带8位拨码设置站IP地址，本体集成4路模拟量输入，16bit精度，电压(±10V)和电流(0~20mA)</td><td>GSIO-B-EIC-4AIH</td></tr><tr><td>2个RJ45口，24VDC供电，带8位拨码设置站IP地址，本体集成8路模拟量输入，16bit精度，电压(±10V)和电流(0~20mA)</td><td>GSIO-B-EIC-8AIH</td></tr><tr><td>2个RJ45口，24VDC供电，带8位拨码设置站IP地址，本体集成4路模拟量输出，16bit精度，电压(±10V)和电流(0~20mA)</td><td>GSIO-B-EIC-4AOH</td></tr><tr><td>2个RJ45口，24VDC供电，带8位拨码设置站IP地址，本体集成8路模拟量输出，16bit精度，电压(±10V)和电流(0~20mA)</td><td>GSIO-B-EIC-8AOH</td></tr><tr><td>2个RJ45口，24VDC供电，带8位拨码设置站IP地址，本体集成4路模拟量输入和4路模拟量输出，电压(±10V)和电流(0~20mA)，16位精度</td><td>GSIO-B-EIC-8AMH</td></tr></table>