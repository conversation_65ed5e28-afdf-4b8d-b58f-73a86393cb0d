#!/usr/bin/env python3
"""
GSIO-B-EIC-1616P 调试和修复脚本
专门解决读取成功但写入失败的问题
"""

import time
from pycomm3 import CIPDriver, Services, ClassCode, UINT

def test_different_output_assemblies(ip_address: str):
    """测试不同的输出Assembly实例号"""
    print(f"测试不同的输出Assembly实例号...")
    
    # 可能的输出Assembly实例号
    possible_assemblies = [150, 151, 152, 101, 102, 103, 200, 201]
    
    test_value = 0x0001  # 测试值：点亮输出0
    
    for assembly in possible_assemblies:
        print(f"\n测试输出Assembly {assembly}...")
        try:
            with CIPDriver(ip_address) as plc:
                result = plc.generic_message(
                    service=Services.set_attribute_single,
                    class_code=ClassCode.assembly,
                    instance=assembly,
                    attribute=3,
                    request_data=UINT.encode(test_value),
                    connected=True,
                    unconnected_send=False,
                    route_path=False,
                    name=f'test_output_assembly_{assembly}'
                )
                
                if result:
                    print(f"✓ Assembly {assembly} 写入成功！")
                    return assembly
                else:
                    print(f"✗ Assembly {assembly} 写入失败: {result.error if result else '无响应'}")
                    
        except Exception as e:
            print(f"✗ Assembly {assembly} 异常: {e}")
    
    return None

def test_different_attributes(ip_address: str, assembly: int = 150):
    """测试不同的属性号"""
    print(f"\n测试Assembly {assembly}的不同属性...")
    
    # 可能的属性号
    possible_attributes = [3, 4, 5, 6, 1, 2]
    
    test_value = 0x0001
    
    for attr in possible_attributes:
        print(f"测试属性 {attr}...")
        try:
            with CIPDriver(ip_address) as plc:
                result = plc.generic_message(
                    service=Services.set_attribute_single,
                    class_code=ClassCode.assembly,
                    instance=assembly,
                    attribute=attr,
                    request_data=UINT.encode(test_value),
                    connected=True,
                    unconnected_send=False,
                    route_path=False,
                    name=f'test_attribute_{attr}'
                )
                
                if result:
                    print(f"✓ 属性 {attr} 写入成功！")
                    return attr
                else:
                    print(f"✗ 属性 {attr} 写入失败: {result.error if result else '无响应'}")
                    
        except Exception as e:
            print(f"✗ 属性 {attr} 异常: {e}")
    
    return None

def test_different_services(ip_address: str):
    """测试不同的服务代码"""
    print(f"\n测试不同的服务代码...")
    
    # 可能的服务
    services_to_test = [
        ("Set_Attribute_Single", Services.set_attribute_single),
        ("Set_Attributes_All", Services.set_attributes_all),
        ("原始服务0x10", 0x10),
        ("原始服务0x02", 0x02),
    ]
    
    test_value = 0x0001
    
    for service_name, service_code in services_to_test:
        print(f"测试服务: {service_name}...")
        try:
            with CIPDriver(ip_address) as plc:
                result = plc.generic_message(
                    service=service_code,
                    class_code=ClassCode.assembly,
                    instance=150,
                    attribute=3,
                    request_data=UINT.encode(test_value),
                    connected=True,
                    unconnected_send=False,
                    route_path=False,
                    name=f'test_service_{service_name}'
                )
                
                if result:
                    print(f"✓ 服务 {service_name} 成功！")
                    return service_code
                else:
                    print(f"✗ 服务 {service_name} 失败: {result.error if result else '无响应'}")
                    
        except Exception as e:
            print(f"✗ 服务 {service_name} 异常: {e}")
    
    return None

def test_raw_bytes_approach(ip_address: str):
    """测试原始字节方法"""
    print(f"\n测试原始字节数据方法...")
    
    import struct
    
    # 不同的数据格式
    test_formats = [
        ("Little Endian UINT16", struct.pack('<H', 0x0001)),
        ("Big Endian UINT16", struct.pack('>H', 0x0001)),
        ("单字节", b'\x01'),
        ("双字节补零", b'\x01\x00'),
    ]
    
    for format_name, data in test_formats:
        print(f"测试格式: {format_name}...")
        try:
            with CIPDriver(ip_address) as plc:
                result = plc.generic_message(
                    service=Services.set_attribute_single,
                    class_code=ClassCode.assembly,
                    instance=150,
                    attribute=3,
                    request_data=data,
                    connected=True,
                    unconnected_send=False,
                    route_path=False,
                    name=f'test_format_{format_name}'
                )
                
                if result:
                    print(f"✓ 格式 {format_name} 成功！")
                    return data
                else:
                    print(f"✗ 格式 {format_name} 失败: {result.error if result else '无响应'}")
                    
        except Exception as e:
            print(f"✗ 格式 {format_name} 异常: {e}")
    
    return None

def test_connection_modes(ip_address: str):
    """重新测试连接模式（针对写入）"""
    print(f"\n重新测试连接模式（针对写入）...")
    
    connection_modes = [
        ("连接模式", True, False, False),
        ("无连接模式", False, True, False),
        ("简化模式", False, False, False),
        ("路径模式", False, True, True),
    ]
    
    test_value = 0x0001
    
    for mode_name, connected, unconnected_send, route_path in connection_modes:
        print(f"测试模式: {mode_name}...")
        try:
            with CIPDriver(ip_address) as plc:
                result = plc.generic_message(
                    service=Services.set_attribute_single,
                    class_code=ClassCode.assembly,
                    instance=150,
                    attribute=3,
                    request_data=UINT.encode(test_value),
                    connected=connected,
                    unconnected_send=unconnected_send,
                    route_path=route_path,
                    name=f'test_mode_{mode_name}'
                )
                
                if result:
                    print(f"✓ 模式 {mode_name} 成功！")
                    return (connected, unconnected_send, route_path)
                else:
                    print(f"✗ 模式 {mode_name} 失败: {result.error if result else '无响应'}")
                    
        except Exception as e:
            print(f"✗ 模式 {mode_name} 异常: {e}")
    
    return None

def main():
    print("=" * 60)
    print("    GSIO-B-EIC-1616P 调试和修复工具")
    print("    专门解决写入输出失败的问题")
    print("=" * 60)
    
    ip_address = input("请输入设备IP地址: ").strip()
    if not ip_address:
        print("未输入IP地址，退出")
        return
    
    print(f"\n开始调试设备: {ip_address}")
    
    # 首先确认读取仍然正常
    print("1. 确认读取输入正常...")
    try:
        with CIPDriver(ip_address) as plc:
            result = plc.generic_message(
                service=Services.get_attribute_single,
                class_code=ClassCode.assembly,
                instance=100,
                attribute=3,
                data_type=UINT,
                connected=True,
                unconnected_send=False,
                route_path=False,
                name='confirm_read'
            )
            
            if result:
                print(f"✓ 读取输入成功: 0x{result.value:04X}")
            else:
                print("✗ 读取输入失败，请检查连接")
                return
    except Exception as e:
        print(f"✗ 读取输入异常: {e}")
        return
    
    # 开始调试写入问题
    print("\n2. 开始调试写入问题...")
    
    # 测试1: 不同的Assembly实例号
    working_assembly = test_different_output_assemblies(ip_address)
    
    # 测试2: 不同的属性号
    if not working_assembly:
        working_attribute = test_different_attributes(ip_address)
    
    # 测试3: 不同的服务代码
    working_service = test_different_services(ip_address)
    
    # 测试4: 原始字节方法
    working_format = test_raw_bytes_approach(ip_address)
    
    # 测试5: 重新测试连接模式
    working_mode = test_connection_modes(ip_address)
    
    # 总结结果
    print("\n" + "=" * 60)
    print("调试结果总结:")
    print("=" * 60)
    
    if working_assembly:
        print(f"✓ 找到有效的输出Assembly: {working_assembly}")
    if working_service:
        print(f"✓ 找到有效的服务代码: {working_service}")
    if working_format:
        print(f"✓ 找到有效的数据格式")
    if working_mode:
        print(f"✓ 找到有效的连接模式: connected={working_mode[0]}, unconnected_send={working_mode[1]}, route_path={working_mode[2]}")
    
    if not any([working_assembly, working_service, working_format, working_mode]):
        print("⚠ 未找到有效的写入方法")
        print("可能的原因:")
        print("1. 设备的输出功能被禁用或锁定")
        print("2. 需要特殊的初始化序列")
        print("3. 设备固件版本问题")
        print("4. EDS文件信息不准确")

if __name__ == "__main__":
    main()
